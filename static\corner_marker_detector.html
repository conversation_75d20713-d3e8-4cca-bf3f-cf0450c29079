<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corner Marker Detector - Phát hiện marker góc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .file-upload {
            border: 3px dashed #4facfe;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }

        .file-upload.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .upload-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .process-section {
            margin-bottom: 30px;
            text-align: center;
        }

        .process-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .process-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .process-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-section {
            display: none;
        }

        .image-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .image-box {
            flex: 1;
            min-width: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: #f9f9f9;
        }

        .image-box h3 {
            background: #4facfe;
            color: white;
            padding: 10px;
            margin: 0;
            text-align: center;
        }

        .image-box img {
            width: 100%;
            height: auto;
            display: block;
        }

        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .marker-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .marker-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .marker-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .marker-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Corner Marker Detector</h1>
            <p>Phát hiện marker góc và cắt ảnh phiếu trắc nghiệm</p>
        </div>

        <div class="content">
            <!-- Upload Section -->
            <div class="upload-section">
                <div class="file-upload" id="fileUpload">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Kéo thả ảnh phiếu trắc nghiệm vào đây hoặc click để chọn</div>
                    <button class="upload-button" type="button">Chọn ảnh</button>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>
            </div>

            <!-- Process Section -->
            <div class="process-section">
                <button class="process-button" id="detectButton" disabled>
                    🔍 Phát hiện Marker Góc
                </button>
                <button class="process-button" id="debugButton" disabled>
                    🐛 Debug Tất cả Contours
                </button>
                <button class="process-button" id="cropButton" disabled>
                    ✂️ Cắt ảnh theo Marker
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Đang xử lý ảnh...</p>
            </div>

            <!-- Status Message -->
            <div id="statusMessage"></div>

            <!-- Results Section -->
            <div class="results-section" id="resultsSection">
                <!-- Marker Information -->
                <div class="marker-info" id="markerInfo" style="display: none;">
                    <h4>📍 Thông tin Marker đã phát hiện:</h4>
                    <div class="marker-list" id="markerList"></div>
                </div>

                <!-- Images Display -->
                <div class="image-container">
                    <div class="image-box">
                        <h3>Ảnh gốc</h3>
                        <img id="originalImage" alt="Ảnh gốc">
                    </div>
                    <div class="image-box">
                        <h3>Marker đã phát hiện</h3>
                        <img id="detectedImage" alt="Marker đã phát hiện">
                    </div>
                    <div class="image-box">
                        <h3>Ảnh đã cắt</h3>
                        <img id="croppedImage" alt="Ảnh đã cắt">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class CornerMarkerDetector {
            constructor() {
                this.initializeElements();
                this.setupEventListeners();
                this.selectedFile = null;
                this.detectedMarkers = null;
            }

            initializeElements() {
                this.fileUpload = document.getElementById('fileUpload');
                this.imageInput = document.getElementById('imageInput');
                this.detectButton = document.getElementById('detectButton');
                this.debugButton = document.getElementById('debugButton');
                this.cropButton = document.getElementById('cropButton');
                this.loading = document.getElementById('loading');
                this.statusMessage = document.getElementById('statusMessage');
                this.resultsSection = document.getElementById('resultsSection');
                this.markerInfo = document.getElementById('markerInfo');
                this.markerList = document.getElementById('markerList');
                this.originalImage = document.getElementById('originalImage');
                this.detectedImage = document.getElementById('detectedImage');
                this.croppedImage = document.getElementById('croppedImage');
            }

            setupEventListeners() {
                // File upload
                this.fileUpload.addEventListener('click', () => this.imageInput.click());
                this.imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
                
                // Drag and drop
                this.fileUpload.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.fileUpload.classList.add('dragover');
                });
                
                this.fileUpload.addEventListener('dragleave', () => {
                    this.fileUpload.classList.remove('dragover');
                });
                
                this.fileUpload.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.fileUpload.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.loadImage(files[0]);
                    }
                });

                // Process buttons
                this.detectButton.addEventListener('click', () => this.detectMarkers());
                this.debugButton.addEventListener('click', () => this.debugContours());
                this.cropButton.addEventListener('click', () => this.cropImage());
            }

            handleImageUpload(event) {
                const file = event.target.files[0];
                if (file) {
                    this.loadImage(file);
                }
            }

            loadImage(file) {
                if (!file.type.startsWith('image/')) {
                    this.showStatus('Vui lòng chọn file ảnh hợp lệ.', 'error');
                    return;
                }

                this.selectedFile = file;
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.originalImage.src = e.target.result;
                    this.detectButton.disabled = false;
                    this.debugButton.disabled = false;
                    this.resultsSection.style.display = 'block';
                    this.showStatus('Ảnh đã được tải. Click "Phát hiện Marker Góc" hoặc "Debug" để tiếp tục.', 'success');
                };
                reader.readAsDataURL(file);
            }

            async detectMarkers() {
                if (!this.selectedFile) return;

                this.showLoading(true);
                this.showStatus('Đang phát hiện marker góc...', 'info');

                const formData = new FormData();
                formData.append('image', this.selectedFile);

                try {
                    const response = await fetch('/api/v1/corner_marker/detect', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    
                    if (response.ok) {
                        this.detectedMarkers = result.markers;
                        this.displayMarkerInfo(result.markers);
                        this.detectedImage.src = 'data:image/jpeg;base64,' + result.detected_image;
                        this.cropButton.disabled = false;
                        this.showStatus(`Đã phát hiện ${result.markers.length} marker góc!`, 'success');
                    } else {
                        this.showStatus(result.detail || 'Lỗi khi phát hiện marker', 'error');
                    }
                } catch (error) {
                    this.showStatus('Lỗi kết nối: ' + error.message, 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            async debugContours() {
                if (!this.selectedFile) return;

                this.showLoading(true);
                this.showStatus('Đang debug tất cả contours...', 'info');

                const formData = new FormData();
                formData.append('image', this.selectedFile);

                try {
                    const response = await fetch('/api/v1/corner_marker/debug', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Hiển thị ảnh debug
                        this.detectedImage.src = `data:image/jpeg;base64,${result.debug_image}`;
                        this.detectedImage.style.display = 'block';

                        // Hiển thị thông tin debug
                        this.markerInfo.innerHTML = `
                            <h3>🐛 Debug Information</h3>
                            <p><strong>Tổng số contours:</strong> ${result.total_contours}</p>
                            <p><strong>Phạm vi diện tích hiện tại:</strong> ${result.area_range_current}</p>
                            <p><strong>Diện tích mục tiêu:</strong> ${result.target_area}</p>
                            <p><strong>Top 20 contours lớn nhất:</strong></p>
                        `;

                        // Hiển thị danh sách contours
                        this.markerList.innerHTML = result.top_20_contours.map((contour, index) => `
                            <div class="marker-item">
                                <strong>Contour ${contour.id}:</strong>
                                Diện tích: ${contour.area},
                                Kích thước: ${contour.width}x${contour.height},
                                Tỷ lệ: ${contour.aspect_ratio},
                                Tọa độ: (${contour.x}, ${contour.y})
                            </div>
                        `).join('');

                        this.showStatus(result.message, 'success');
                    } else {
                        this.showStatus('Lỗi debug contours', 'error');
                    }
                } catch (error) {
                    console.error('Debug error:', error);
                    this.showStatus('Lỗi kết nối khi debug', 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            async cropImage() {
                if (!this.selectedFile || !this.detectedMarkers) return;

                this.showLoading(true);
                this.showStatus('Đang cắt ảnh theo marker...', 'info');

                const formData = new FormData();
                formData.append('image', this.selectedFile);

                try {
                    const response = await fetch('/api/v1/corner_marker/crop', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    
                    if (response.ok) {
                        this.croppedImage.src = 'data:image/jpeg;base64,' + result.cropped_image;
                        this.showStatus('Ảnh đã được cắt thành công!', 'success');
                    } else {
                        this.showStatus(result.detail || 'Lỗi khi cắt ảnh', 'error');
                    }
                } catch (error) {
                    this.showStatus('Lỗi kết nối: ' + error.message, 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            displayMarkerInfo(markers) {
                this.markerList.innerHTML = '';
                markers.forEach((marker, index) => {
                    const markerItem = document.createElement('div');
                    markerItem.className = 'marker-item';
                    markerItem.innerHTML = `
                        <strong>Marker ${index + 1}</strong><br>
                        X: ${marker.x}<br>
                        Y: ${marker.y}<br>
                        Kích thước: ${marker.area}
                    `;
                    this.markerList.appendChild(markerItem);
                });
                this.markerInfo.style.display = 'block';
            }

            showStatus(message, type) {
                this.statusMessage.innerHTML = `<div class="status-${type}">${message}</div>`;
            }

            showLoading(show) {
                this.loading.style.display = show ? 'block' : 'none';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new CornerMarkerDetector();
        });
    </script>
</body>
</html>
