"""
<PERSON><PERSON><PERSON> nghĩa các vùng cắt cố định cho OMR sau khi perspective transform
Tọa độ dựa trên ảnh đã chuẩn hóa về kích thước 2480x3508 (A4 300 DPI)
"""

# Kích thước ảnh chuẩn sau perspective transform
STANDARD_WIDTH = 2480
STANDARD_HEIGHT = 3508

# Vùng thông tin cá nhân (phần trên)
STUDENT_INFO_REGION = {
    # Vùng số báo danh (SBD) - 8 chữ số, mỗi chữ số có 10 ô (0-9)
    "SBD_BOX": (150, 300, 600, 800),  # x, y, w, h
    
    # Vùng mã đề thi - 4 chữ số, mỗi chữ số có 10 ô (0-9)  
    "TEST_CODE_BOX": (1800, 300, 300, 400),  # x, y, w, h
    
    # Thông tin bổ sung
    "HEADER_BOX": (800, 200, 800, 200)  # Tê<PERSON>, lớp, trường...
}

# Vùng câu trả lời (phần dưới)
ANSWER_REGIONS = {
    # Phần I: 40 câu trắc nghiệm (4 cột x 10 hàng)
    "PART_1": {
        "total_questions": 40,
        "layout": "4x10",  # 4 cột, 10 hàng
        "box": (100, 1200, 2280, 1000),  # x, y, w, h - toàn bộ vùng Part I
        "columns": [
            (100, 1200, 570, 1000),   # Cột 1: câu 1-10
            (670, 1200, 570, 1000),   # Cột 2: câu 11-20  
            (1240, 1200, 570, 1000),  # Cột 3: câu 21-30
            (1810, 1200, 570, 1000),  # Cột 4: câu 31-40
        ]
    },
    
    # Phần II: 8 câu tự luận dạng trắc nghiệm
    "PART_2": {
        "total_questions": 8,
        "layout": "table",  # Dạng bảng
        "box": (100, 2300, 2280, 600),  # x, y, w, h
        "rows": [
            (100, 2300, 2280, 75),  # Câu 1
            (100, 2375, 2280, 75),  # Câu 2
            (100, 2450, 2280, 75),  # Câu 3
            (100, 2525, 2280, 75),  # Câu 4
            (100, 2600, 2280, 75),  # Câu 5
            (100, 2675, 2280, 75),  # Câu 6
            (100, 2750, 2280, 75),  # Câu 7
            (100, 2825, 2280, 75),  # Câu 8
        ]
    },
    
    # Phần III: 6 câu điền số (mỗi câu có 4 chữ số 0-9)
    "PART_3": {
        "total_questions": 6,
        "layout": "digits",  # Điền chữ số
        "box": (100, 3000, 2280, 400),  # x, y, w, h
        "questions": [
            (100, 3000, 380, 65),   # Câu 1
            (480, 3000, 380, 65),   # Câu 2
            (860, 3000, 380, 65),   # Câu 3
            (1240, 3000, 380, 65),  # Câu 4
            (1620, 3000, 380, 65),  # Câu 5
            (2000, 3000, 380, 65),  # Câu 6
        ]
    }
}

# Cấu hình cho việc phát hiện bubble đã tô
BUBBLE_CONFIG = {
    "fill_threshold": 0.6,  # Ngưỡng để xác định bubble đã tô (60% pixel đen)
    "min_bubble_area": 20,   # Diện tích tối thiểu của bubble
    "max_bubble_area": 500,  # Diện tích tối đa của bubble
    "circularity_threshold": 0.5  # Ngưỡng độ tròn
}

# Cấu hình cho SBD và mã đề
DIGIT_CONFIG = {
    "sbd_digits": 8,      # SBD có 8 chữ số
    "test_code_digits": 4, # Mã đề có 4 chữ số
    "digit_options": 10,   # Mỗi chữ số có 10 lựa chọn (0-9)
}

# Cấu hình cho Part I (trắc nghiệm)
PART1_CONFIG = {
    "questions_per_column": 10,
    "options_per_question": 4,  # A, B, C, D
    "option_labels": ["A", "B", "C", "D"]
}

# Cấu hình cho Part II (tự luận dạng trắc nghiệm)
PART2_CONFIG = {
    "sub_options_per_question": 4,  # a, b, c, d
    "sub_option_labels": ["a", "b", "c", "d"]
}

# Cấu hình cho Part III (điền số)
PART3_CONFIG = {
    "digits_per_question": 4,  # Mỗi câu có 4 chữ số
    "digit_options": 10        # 0-9
}

def get_region_coordinates(region_name: str, index: int = None):
    """
    Lấy tọa độ của một vùng cụ thể
    
    Args:
        region_name: Tên vùng ('SBD_BOX', 'TEST_CODE_BOX', 'PART_1', etc.)
        index: Chỉ số cho các vùng có nhiều phần tử (cột, hàng, câu hỏi)
    
    Returns:
        tuple: (x, y, w, h) hoặc None nếu không tìm thấy
    """
    if region_name in STUDENT_INFO_REGION:
        return STUDENT_INFO_REGION[region_name]
    
    if region_name in ANSWER_REGIONS:
        region = ANSWER_REGIONS[region_name]
        if index is not None:
            if "columns" in region and index < len(region["columns"]):
                return region["columns"][index]
            elif "rows" in region and index < len(region["rows"]):
                return region["rows"][index]
            elif "questions" in region and index < len(region["questions"]):
                return region["questions"][index]
        return region.get("box")
    
    return None

def validate_coordinates():
    """Kiểm tra tính hợp lệ của các tọa độ đã định nghĩa"""
    errors = []
    
    # Kiểm tra các vùng không vượt quá kích thước ảnh
    for region_name, coords in STUDENT_INFO_REGION.items():
        x, y, w, h = coords
        if x + w > STANDARD_WIDTH or y + h > STANDARD_HEIGHT:
            errors.append(f"{region_name}: Vượt quá kích thước ảnh chuẩn")
    
    # Kiểm tra các vùng câu trả lời
    for part_name, part_config in ANSWER_REGIONS.items():
        box = part_config.get("box")
        if box:
            x, y, w, h = box
            if x + w > STANDARD_WIDTH or y + h > STANDARD_HEIGHT:
                errors.append(f"{part_name}: Vượt quá kích thước ảnh chuẩn")
    
    return errors
