<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H<PERSON> thống chấm điểm tự động - PlanBook AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .upload-group {
            margin-bottom: 25px;
        }

        .upload-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            font-size: 1.1em;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .file-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1em;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .result-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .result-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .result-header h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .result-body {
            padding: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .info-item label {
            font-weight: 600;
            color: #666;
            display: block;
            margin-bottom: 5px;
        }

        .info-item value {
            font-size: 1.1em;
            color: #333;
        }

        .score-display {
            text-align: center;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .score-display .score {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .file-preview {
            margin-top: 15px;
        }

        .file-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-icon {
            margin-right: 10px;
            color: #4facfe;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Hệ thống chấm điểm tự động</h1>
            <p>Upload ảnh phiếu trả lời và file đáp án để chấm điểm tự động</p>
        </div>

        <div class="main-content">
            <form id="gradingForm" enctype="multipart/form-data">
                <div class="upload-section">
                    <div class="upload-group">
                        <label for="imageFiles">📄 Chọn ảnh phiếu trả lời (có thể chọn nhiều file):</label>
                        <input type="file" id="imageFiles" name="image_files" class="file-input" 
                               multiple accept="image/*" required>
                        <div id="imagePreview" class="file-preview"></div>
                    </div>

                    <div class="upload-group">
                        <label for="excelFile">📊 Chọn file Excel đáp án:</label>
                        <input type="file" id="excelFile" name="excel_file" class="file-input" 
                               accept=".xlsx,.xls" required>
                        <div id="excelPreview" class="file-preview"></div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        🚀 Bắt đầu chấm điểm
                    </button>
                </div>
            </form>

            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <h3>Đang xử lý chấm điểm...</h3>
                <p>Vui lòng đợi trong giây lát</p>
            </div>

            <div class="results-section" id="resultsSection">
                <h2>📊 Kết quả chấm điểm</h2>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <script>
        // API base URL - thay đổi theo cấu hình của bạn
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        // Elements
        const form = document.getElementById('gradingForm');
        const imageFiles = document.getElementById('imageFiles');
        const excelFile = document.getElementById('excelFile');
        const submitBtn = document.getElementById('submitBtn');
        const loadingSection = document.getElementById('loadingSection');
        const resultsSection = document.getElementById('resultsSection');
        const resultsContainer = document.getElementById('resultsContainer');
        const imagePreview = document.getElementById('imagePreview');
        const excelPreview = document.getElementById('excelPreview');

        // File preview handlers
        imageFiles.addEventListener('change', function() {
            showFilePreview(this.files, imagePreview, 'image');
        });

        excelFile.addEventListener('change', function() {
            showFilePreview(this.files, excelPreview, 'excel');
        });

        function showFilePreview(files, container, type) {
            container.innerHTML = '';
            if (files.length > 0) {
                const fileList = document.createElement('div');
                fileList.className = 'file-list';
                
                Array.from(files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span class="file-icon">${type === 'image' ? '🖼️' : '📊'}</span>
                        <span>${file.name} (${formatFileSize(file.size)})</span>
                    `;
                    fileList.appendChild(fileItem);
                });
                
                container.appendChild(fileList);
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            
            // Add image files
            Array.from(imageFiles.files).forEach(file => {
                formData.append('image_files', file);
            });
            
            // Add excel file
            formData.append('excel_file', excelFile.files[0]);
            
            // Show loading
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE_URL}/auto_grading/auto`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResults(result);
                } else {
                    showError('Lỗi từ server: ' + (result.detail || 'Unknown error'));
                }
            } catch (error) {
                showError('Lỗi kết nối: ' + error.message);
            } finally {
                hideLoading();
            }
        });

        function showLoading() {
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ Đang xử lý...';
            loadingSection.style.display = 'block';
            resultsSection.style.display = 'none';
        }

        function hideLoading() {
            submitBtn.disabled = false;
            submitBtn.textContent = '🚀 Bắt đầu chấm điểm';
            loadingSection.style.display = 'none';
        }

        function showResults(data) {
            resultsContainer.innerHTML = '';
            resultsSection.style.display = 'block';
            
            if (data.results && data.results.length > 0) {
                data.results.forEach((result, index) => {
                    const resultCard = createResultCard(result, index + 1);
                    resultsContainer.appendChild(resultCard);
                });
            } else {
                resultsContainer.innerHTML = '<p>Không có kết quả nào được trả về.</p>';
            }
        }

        function createResultCard(result, index) {
            const card = document.createElement('div');
            card.className = 'result-card';
            
            const isSuccess = result.status === 'success';
            const statusClass = isSuccess ? 'status-success' : 'status-failed';
            
            card.innerHTML = `
                <div class="result-header">
                    <h3>📄 Bài làm ${index}: ${result.filename}</h3>
                    <span class="status-badge ${statusClass}">
                        ${isSuccess ? '✅ Thành công' : '❌ Thất bại'}
                    </span>
                </div>
                <div class="result-body">
                    ${isSuccess ? createSuccessContent(result) : createErrorContent(result)}
                </div>
            `;
            
            return card;
        }

        function createSuccessContent(result) {
            return `
                <div class="info-grid">
                    <div class="info-item">
                        <label>Mã học sinh:</label>
                        <value>${result.student_info?.student_code || 'N/A'}</value>
                    </div>
                    <div class="info-item">
                        <label>Mã đề thi:</label>
                        <value>${result.student_info?.test_code || 'N/A'}</value>
                    </div>
                    <div class="info-item">
                        <label>Số câu đúng:</label>
                        <value>${result.correct_count}/${result.total_questions}</value>
                    </div>
                    <div class="info-item">
                        <label>Số câu sai:</label>
                        <value>${result.incorrect_count}</value>
                    </div>
                    <div class="info-item">
                        <label>Số câu bỏ trống:</label>
                        <value>${result.blank_count}</value>
                    </div>
                </div>
                
                <div class="score-display">
                    <div class="score">${result.score}/10</div>
                    <div>Điểm số</div>
                </div>
                
                <details>
                    <summary style="cursor: pointer; font-weight: 600; margin: 15px 0;">
                        📝 Chi tiết từng câu hỏi
                    </summary>
                    <div style="margin-top: 15px;">
                        ${createDetailedResults(result.detailed_results)}
                    </div>
                </details>
            `;
        }

        function createErrorContent(result) {
            return `
                <div class="error-message">
                    <strong>Lỗi:</strong> ${result.error}
                </div>
                ${result.student_info ? `
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Mã học sinh:</label>
                            <value>${result.student_info.student_code || 'N/A'}</value>
                        </div>
                        <div class="info-item">
                            <label>Mã đề thi:</label>
                            <value>${result.student_info.test_code || 'N/A'}</value>
                        </div>
                    </div>
                ` : ''}
            `;
        }

        function createDetailedResults(detailedResults) {
            if (!detailedResults) return '<p>Không có chi tiết.</p>';
            
            let html = '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">';
            
            Object.entries(detailedResults).forEach(([questionNum, details]) => {
                const resultClass = details.result === 'correct' ? 'status-success' : 
                                  details.result === 'incorrect' ? 'status-failed' : 'status-blank';
                const icon = details.result === 'correct' ? '✅' : 
                           details.result === 'incorrect' ? '❌' : '⚪';
                
                html += `
                    <div class="info-item">
                        <label>Câu ${questionNum}: ${icon}</label>
                        <value>
                            Trả lời: ${details.student_answer || 'Trống'}<br>
                            Đáp án: ${details.correct_answer}
                        </value>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        function showError(message) {
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <strong>Có lỗi xảy ra:</strong> ${message}
                </div>
            `;
            resultsSection.style.display = 'block';
        }
    </script>
</body>
</html>
