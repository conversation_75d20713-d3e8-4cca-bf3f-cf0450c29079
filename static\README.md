# <PERSON>ệ thống chấm điểm tự động - Auto Grading Interface

## <PERSON><PERSON> tả
Giao diện web để sử dụng hệ thống chấm điểm tự động của PlanBook AI. <PERSON>ệ thống này cho phép upload ảnh phiếu trả lời và file Excel đáp án để chấm điểm tự động.

## Cách sử dụng

### 1. Khởi động server
```bash
# Chạy FastAPI server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Truy cập giao diện
- Mở trình duyệt và truy cập: `http://localhost:8000/auto-grading`
- Hoặc truy cập API docs: `http://localhost:8000/api/v1/docs`

### 3. Chuẩn bị dữ liệu

#### File Excel đáp án
File Excel cần có cấu trúc như sau:
- Cột A: <PERSON>ã đề thi (test_code)
- Cột B trở đi: <PERSON><PERSON><PERSON> án cho từng câu hỏi

Ví dụ:
```
test_code | question_1 | question_2 | question_3 | ...
001       | A          | B          | C          | ...
002       | B          | A          | D          | ...
```

#### Ảnh phiếu trả lời
- Định dạng: JPG, PNG, JPEG
- Chất lượng: Rõ nét, không bị mờ
- Góc chụp: Thẳng, không bị nghiêng quá nhiều
- Ánh sáng: Đủ sáng, không bị tối hoặc chói

### 4. Quy trình chấm điểm

1. **Upload files**: Chọn ảnh phiếu trả lời (có thể chọn nhiều file) và file Excel đáp án
2. **Submit**: Nhấn nút "Bắt đầu chấm điểm"
3. **Xử lý**: Hệ thống sẽ:
   - Phân tích ảnh bằng OMR (Optical Mark Recognition)
   - Trích xuất thông tin học sinh và mã đề
   - Nhận diện các câu trả lời đã tô
   - So sánh với đáp án trong file Excel
   - Tính điểm và tạo báo cáo chi tiết
4. **Kết quả**: Xem kết quả chấm điểm với:
   - Thông tin học sinh và mã đề
   - Điểm số tổng kết
   - Số câu đúng/sai/bỏ trống
   - Chi tiết từng câu hỏi

## Tính năng

### ✅ Đã hoàn thành
- [x] Giao diện upload file thân thiện
- [x] Preview file đã chọn
- [x] Gọi API auto grading
- [x] Hiển thị kết quả chi tiết
- [x] Xử lý lỗi và thông báo
- [x] Responsive design
- [x] Loading animation

### 🔄 Đang phát triển
- [ ] Xem trước ảnh đã xử lý (processed images)
- [ ] Tải xuống báo cáo PDF
- [ ] Lưu lịch sử chấm điểm
- [ ] Xuất kết quả Excel

## API Endpoints

### POST `/api/v1/auto_grading/auto`
Chấm điểm tự động cho danh sách ảnh phiếu trả lời.

**Parameters:**
- `image_files`: List[UploadFile] - Danh sách ảnh phiếu trả lời
- `excel_file`: UploadFile - File Excel chứa đáp án

**Response:**
```json
{
  "message": "Grading completed successfully",
  "results": [
    {
      "filename": "student_001.jpg",
      "student_info": {
        "student_code": "12345678",
        "test_code": "001"
      },
      "score": 8.5,
      "total_questions": 40,
      "correct_count": 34,
      "incorrect_count": 4,
      "blank_count": 2,
      "status": "success"
    }
  ]
}
```

## Cấu trúc file

```
static/
├── auto_grading_interface.html  # Giao diện chính
└── README.md                    # Tài liệu hướng dẫn
```

## Yêu cầu hệ thống

- Python 3.8+
- FastAPI
- OpenCV (cho xử lý OMR)
- Tesseract OCR
- Pandas (cho xử lý Excel)

## Troubleshooting

### Lỗi thường gặp

1. **"Test code not found in answer keys"**
   - Kiểm tra mã đề trong file Excel có khớp với mã đề trên phiếu trả lời
   - Đảm bảo format mã đề đúng (ví dụ: "001" thay vì "1")

2. **"Invalid image format"**
   - Kiểm tra định dạng ảnh (chỉ hỗ trợ JPG, PNG, JPEG)
   - Đảm bảo ảnh không bị hỏng

3. **"Connection error"**
   - Kiểm tra server FastAPI đã chạy chưa
   - Kiểm tra URL API trong file HTML (mặc định: http://localhost:8000)

4. **"OMR processing failed"**
   - Kiểm tra chất lượng ảnh
   - Đảm bảo phiếu trả lời có đủ marker để nhận diện
   - Kiểm tra ánh sáng và góc chụp

### Cấu hình

Để thay đổi URL API, sửa biến `API_BASE_URL` trong file HTML:
```javascript
const API_BASE_URL = 'http://your-server:port/api/v1';
```

## Liên hệ hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs server
2. Xem API documentation tại `/api/v1/docs`
3. Liên hệ team phát triển
