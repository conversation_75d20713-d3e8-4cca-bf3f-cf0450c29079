from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import base64
import cv2
import numpy as np
import json
import os
from app.services.omr_processor import OMRProcessor
import logging

logger = logging.getLogger(__name__)

# Pydantic models for region management
class RegionCoordinates(BaseModel):
    x: int
    y: int
    w: int
    h: int

class ImageDimensions(BaseModel):
    width: int
    height: int

class SaveRegionRequest(BaseModel):
    region_type: str
    coordinates: RegionCoordinates
    image_dimensions: ImageDimensions

# File to store custom regions
CUSTOM_REGIONS_FILE = "custom_regions.json"

router = APIRouter(
    prefix="/omr_debug",
    tags=["OMR Debug"]
)

@router.post("/viewer")
async def omr_debug_viewer(
    image_file: UploadFile = File(..., description="Answer sheet image for OMR processing")
):
    """
    API để xử lý OMR và trả về ảnh đã xử lý cùng thông tin trích xuất.
    Chỉ cần upload ảnh phiếu trả lời, không cần file Excel đáp án.
    """
    try:
        # Kiểm tra file type
        if not image_file.filename or not any(
            image_file.filename.lower().endswith(ext) 
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']
        ):
            raise HTTPException(
                status_code=400, 
                detail="Only image files (JPG, JPEG, PNG, BMP) are supported"
            )

        # Đọc nội dung ảnh
        image_content = await image_file.read()
        
        if len(image_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")

        logger.info(f"Processing OMR for image: {image_file.filename}")

        # Khởi tạo OMR processor
        omr_processor = OMRProcessor()
        
        # Chuyển đổi bytes thành image
        nparr = np.frombuffer(image_content, np.uint8)
        original_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if original_image is None:
            return {
                "success": False,
                "error": "invalid image",
                "message": "Cannot decode image file. Please check if the image is valid."
            }

        # Xử lý OMR và lấy ảnh từng bước
        processing_steps = await process_omr_with_debug_images(omr_processor, original_image)
        
        # Trích xuất thông tin
        try:
            student_info, student_answers = await omr_processor.process_image(image_content)
            
            # Tạo final report
            final_report = {
                "student_code": student_info.get('student_id', 'N/A'),
                "test_code": student_info.get('test_code', 'N/A'),
                "extracted_answers": student_answers,
                "total_answers": len(student_answers),
                "processing_status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error extracting information: {str(e)}")
            final_report = {
                "student_code": "N/A",
                "test_code": "N/A", 
                "extracted_answers": {},
                "total_answers": 0,
                "processing_status": "failed",
                "error": str(e)
            }

        return {
            "success": True,
            "filename": image_file.filename,
            "processed_images": processing_steps,
            "final_report": final_report,
            "message": "OMR processing completed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in OMR debug viewer: {str(e)}")
        return {
            "success": False,
            "error": "processing failed",
            "message": f"OMR processing failed: {str(e)}"
        }


async def process_omr_with_debug_images(omr_processor: OMRProcessor, original_image: np.ndarray) -> List[Dict[str, Any]]:
    """
    Xử lý OMR và trả về ảnh từng bước xử lý
    """
    processing_steps = []
    
    try:
        # Bước 1: Ảnh gốc
        original_b64 = image_to_base64(original_image)
        processing_steps.append({
            "step": 1,
            "name": "Original Image",
            "description": "Ảnh phiếu trả lời gốc",
            "image": original_b64
        })

        # Bước 2: Tiền xử lý (grayscale, denoise, enhance)
        preprocessed = omr_processor.preprocess_image(original_image)
        preprocessed_b64 = image_to_base64(preprocessed, is_gray=True)
        processing_steps.append({
            "step": 2,
            "name": "Preprocessed Image", 
            "description": "Ảnh sau tiền xử lý (grayscale, khử nhiễu, tăng cường độ tương phản)",
            "image": preprocessed_b64
        })

        # Bước 3: Căn chỉnh ảnh (perspective transform)
        try:
            aligned = omr_processor.align_image(preprocessed)
            aligned_b64 = image_to_base64(aligned, is_gray=True)
            processing_steps.append({
                "step": 3,
                "name": "Aligned Image",
                "description": "Ảnh sau căn chỉnh góc độ và perspective transform",
                "image": aligned_b64
            })
        except Exception as e:
            logger.warning(f"Alignment failed: {str(e)}, using preprocessed image")
            aligned = preprocessed
            processing_steps.append({
                "step": 3,
                "name": "Aligned Image (Failed)",
                "description": f"Căn chỉnh thất bại: {str(e)}, sử dụng ảnh đã tiền xử lý",
                "image": preprocessed_b64
            })

        # Bước 4: Phát hiện markers (nếu có)
        try:
            markers_image = detect_and_draw_markers(aligned)
            markers_b64 = image_to_base64(markers_image, is_gray=True)
            processing_steps.append({
                "step": 4,
                "name": "Detected Markers",
                "description": "Ảnh với các markers đã được phát hiện và đánh dấu",
                "image": markers_b64
            })
        except Exception as e:
            logger.warning(f"Marker detection failed: {str(e)}")
            processing_steps.append({
                "step": 4,
                "name": "Marker Detection (Failed)",
                "description": f"Phát hiện markers thất bại: {str(e)}",
                "image": aligned_b64
            })

        # Bước 5: Hiển thị các vùng đã cắt (Region Extraction)
        try:
            regions_image = draw_extraction_regions(aligned)
            regions_b64 = image_to_base64(regions_image, is_gray=False)
            processing_steps.append({
                "step": 5,
                "name": "Extraction Regions",
                "description": "Ảnh với các vùng trích xuất được đánh dấu (SBD, Mã đề, Part I, II, III)",
                "image": regions_b64
            })
        except Exception as e:
            logger.warning(f"Region extraction visualization failed: {str(e)}")
            processing_steps.append({
                "step": 5,
                "name": "Region Extraction (Failed)",
                "description": f"Hiển thị vùng trích xuất thất bại: {str(e)}",
                "image": aligned_b64
            })

    except Exception as e:
        logger.error(f"Error in processing steps: {str(e)}")
        # Trả về ít nhất ảnh gốc
        if not processing_steps:
            original_b64 = image_to_base64(original_image)
            processing_steps.append({
                "step": 1,
                "name": "Original Image",
                "description": "Ảnh phiếu trả lời gốc",
                "image": original_b64
            })

    return processing_steps


def image_to_base64(image: np.ndarray, is_gray: bool = False) -> str:
    """Chuyển đổi ảnh OpenCV thành base64 string"""
    try:
        if is_gray and len(image.shape) == 2:
            # Grayscale image
            _, buffer = cv2.imencode('.png', image)
        else:
            # Color image
            _, buffer = cv2.imencode('.png', image)
        
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/png;base64,{image_base64}"
    except Exception as e:
        logger.error(f"Error converting image to base64: {str(e)}")
        return ""


def detect_and_draw_markers(image: np.ndarray) -> np.ndarray:
    """Phát hiện và vẽ markers trên ảnh"""
    try:
        # Tạo bản sao để vẽ
        result = image.copy()
        if len(result.shape) == 2:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)

        # Threshold ảnh để tìm contours (nếu ảnh là grayscale)
        if len(image.shape) == 2:
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        else:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Tìm contours trên ảnh binary
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Lọc contours có thể là markers (hình vuông, kích thước phù hợp)
        large_markers = []
        small_markers = []

        for contour in contours:
            area = cv2.contourArea(contour)

            # Lọc theo diện tích (marker phải có kích thước vừa phải)
            if 1000 < area < 10000:  # Large markers
                # Xấp xỉ contour thành đa giác
                epsilon = 0.04 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                # Marker phải là hình vuông (4 góc)
                if len(approx) == 4:
                    # Kiểm tra tỷ lệ khung hình gần vuông
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h

                    if 0.8 <= aspect_ratio <= 1.2:  # Gần hình vuông
                        large_markers.append(contour)

            elif 200 < area < 1000:  # Small markers
                epsilon = 0.04 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h

                    if 0.85 <= aspect_ratio <= 1.15:  # Gần hình vuông hơn cho small markers
                        small_markers.append(contour)

        # Vẽ large markers (màu xanh lá)
        cv2.drawContours(result, large_markers, -1, (0, 255, 0), 3)

        # Vẽ small markers (màu vàng)
        cv2.drawContours(result, small_markers, -1, (0, 255, 255), 2)

        # Đánh số large markers
        for i, marker in enumerate(large_markers):
            M = cv2.moments(marker)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result, f"L{i+1}", (cx-15, cy+5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Đánh số small markers
        for i, marker in enumerate(small_markers):
            M = cv2.moments(marker)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result, f"S{i+1}", (cx-10, cy+3),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # Thêm legend
        cv2.putText(result, f"Large Markers: {len(large_markers)}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(result, f"Small Markers: {len(small_markers)}", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return result

    except Exception as e:
        logger.error(f"Error in marker detection: {str(e)}")
        return image


def detect_and_draw_bubbles(image: np.ndarray) -> np.ndarray:
    """Phát hiện và vẽ bubbles trên ảnh"""
    try:
        # Tạo bản sao để vẽ
        result = image.copy()
        if len(result.shape) == 2:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)
        
        # Threshold ảnh để tìm contours (nếu ảnh là grayscale)
        if len(image.shape) == 2:
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        else:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Tìm contours
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Lọc contours có thể là bubbles (hình tròn, kích thước nhỏ)
        bubbles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 20 < area < 500:  # Kích thước bubble
                # Kiểm tra độ tròn
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    if circularity > 0.5:  # Khá tròn
                        bubbles.append(contour)
        
        # Vẽ bubbles
        cv2.drawContours(result, bubbles, -1, (0, 0, 255), 1)
        
        return result
        
    except Exception as e:
        logger.error(f"Error in bubble detection: {str(e)}")
        return image


def draw_extraction_regions(image: np.ndarray) -> np.ndarray:
    """Vẽ các vùng trích xuất trên ảnh"""
    try:
        from app.constants.omr_regions import STUDENT_INFO_REGION, ANSWER_REGIONS

        # Tạo bản sao để vẽ
        result = image.copy()
        if len(result.shape) == 2:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)

        # Vẽ vùng thông tin sinh viên (màu xanh dương)
        sbd_box = STUDENT_INFO_REGION["SBD_BOX"]
        cv2.rectangle(result, (sbd_box[0], sbd_box[1]),
                     (sbd_box[0] + sbd_box[2], sbd_box[1] + sbd_box[3]),
                     (255, 0, 0), 3)
        cv2.putText(result, "SBD", (sbd_box[0], sbd_box[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)

        test_code_box = STUDENT_INFO_REGION["TEST_CODE_BOX"]
        cv2.rectangle(result, (test_code_box[0], test_code_box[1]),
                     (test_code_box[0] + test_code_box[2], test_code_box[1] + test_code_box[3]),
                     (255, 0, 0), 3)
        cv2.putText(result, "TEST CODE", (test_code_box[0], test_code_box[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)

        # Vẽ vùng Part I (màu xanh lá)
        part1_box = ANSWER_REGIONS["PART_1"]["box"]
        cv2.rectangle(result, (part1_box[0], part1_box[1]),
                     (part1_box[0] + part1_box[2], part1_box[1] + part1_box[3]),
                     (0, 255, 0), 3)
        cv2.putText(result, "PART I (40 questions)", (part1_box[0], part1_box[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # Vẽ các cột của Part I
        for i, col_box in enumerate(ANSWER_REGIONS["PART_1"]["columns"]):
            cv2.rectangle(result, (col_box[0], col_box[1]),
                         (col_box[0] + col_box[2], col_box[1] + col_box[3]),
                         (0, 200, 0), 2)
            cv2.putText(result, f"Col {i+1}", (col_box[0] + 10, col_box[1] + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 200, 0), 1)

        # Vẽ vùng Part II (màu đỏ)
        part2_box = ANSWER_REGIONS["PART_2"]["box"]
        cv2.rectangle(result, (part2_box[0], part2_box[1]),
                     (part2_box[0] + part2_box[2], part2_box[1] + part2_box[3]),
                     (0, 0, 255), 3)
        cv2.putText(result, "PART II (8 questions)", (part2_box[0], part2_box[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        # Vẽ vùng Part III (màu tím)
        part3_box = ANSWER_REGIONS["PART_3"]["box"]
        cv2.rectangle(result, (part3_box[0], part3_box[1]),
                     (part3_box[0] + part3_box[2], part3_box[1] + part3_box[3]),
                     (255, 0, 255), 3)
        cv2.putText(result, "PART III (6 questions)", (part3_box[0], part3_box[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 255), 2)

        # Thêm legend
        legend_y = 50
        cv2.putText(result, "Regions:", (10, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(result, "SBD & Test Code", (10, legend_y + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        cv2.putText(result, "Part I (40 MC)", (10, legend_y + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        cv2.putText(result, "Part II (8 Essay)", (10, legend_y + 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        cv2.putText(result, "Part III (6 Digits)", (10, legend_y + 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

        return result

    except Exception as e:
        logger.error(f"Error drawing extraction regions: {str(e)}")
        return image


# Region Management APIs
@router.post("/save_region")
async def save_region(request: SaveRegionRequest):
    """Lưu tọa độ vùng do người dùng định nghĩa"""
    try:
        # Load existing regions
        regions = load_custom_regions()

        # Save new region
        regions[request.region_type] = {
            "coordinates": request.coordinates.dict(),
            "reference_dimensions": request.image_dimensions.dict()
        }

        # Save to file
        save_custom_regions(regions)

        logger.info(f"Saved region {request.region_type}: {request.coordinates.dict()}")

        return {
            "success": True,
            "message": f"Vùng {request.region_type} đã được lưu thành công"
        }

    except Exception as e:
        logger.error(f"Error saving region: {str(e)}")
        return {
            "success": False,
            "message": f"Lỗi khi lưu vùng: {str(e)}"
        }


@router.post("/reset_regions")
async def reset_regions():
    """Xóa tất cả các vùng đã định nghĩa"""
    try:
        if os.path.exists(CUSTOM_REGIONS_FILE):
            os.remove(CUSTOM_REGIONS_FILE)

        return {
            "success": True,
            "message": "Đã xóa tất cả các vùng"
        }

    except Exception as e:
        logger.error(f"Error resetting regions: {str(e)}")
        return {
            "success": False,
            "message": f"Lỗi khi xóa vùng: {str(e)}"
        }


@router.post("/test_regions")
async def test_regions(image: UploadFile = File(...)):
    """Test các vùng đã định nghĩa với ảnh mới"""
    try:
        # Read image
        image_content = await image.read()
        nparr = np.frombuffer(image_content, np.uint8)
        test_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if test_image is None:
            return {
                "success": False,
                "message": "Không thể đọc ảnh"
            }

        # Load custom regions
        custom_regions = load_custom_regions()

        if not custom_regions:
            return {
                "success": False,
                "message": "Chưa có vùng nào được định nghĩa"
            }

        # Process image with OMR
        omr_processor = OMRProcessor()
        processed_image = omr_processor.preprocess_image(test_image)
        aligned_image = omr_processor.align_image(processed_image)

        # Apply custom regions and extract
        result_image, extracted_data = apply_custom_regions(aligned_image, custom_regions)

        # Convert result image to base64
        result_b64 = image_to_base64(result_image, is_gray=False)

        return {
            "success": True,
            "test_image": result_b64,
            "extracted_data": extracted_data,
            "message": "Test thành công"
        }

    except Exception as e:
        logger.error(f"Error testing regions: {str(e)}")
        return {
            "success": False,
            "message": f"Lỗi khi test: {str(e)}"
        }


def load_custom_regions() -> Dict:
    """Load custom regions from file"""
    try:
        if os.path.exists(CUSTOM_REGIONS_FILE):
            with open(CUSTOM_REGIONS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading custom regions: {str(e)}")

    return {}


def save_custom_regions(regions: Dict):
    """Save custom regions to file"""
    try:
        with open(CUSTOM_REGIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(regions, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Error saving custom regions: {str(e)}")
        raise


def apply_custom_regions(image: np.ndarray, custom_regions: Dict) -> tuple:
    """Apply custom regions to extract information"""
    try:
        # Create result image for visualization
        result_image = image.copy()
        if len(result_image.shape) == 2:
            result_image = cv2.cvtColor(result_image, cv2.COLOR_GRAY2BGR)

        extracted_data = {}
        current_height, current_width = image.shape[:2]

        for region_type, region_info in custom_regions.items():
            coords = region_info["coordinates"]
            ref_dims = region_info["reference_dimensions"]

            # Calculate scale factors
            scale_x = current_width / ref_dims["width"]
            scale_y = current_height / ref_dims["height"]

            # Apply scaling to coordinates
            scaled_x = int(coords["x"] * scale_x)
            scaled_y = int(coords["y"] * scale_y)
            scaled_w = int(coords["w"] * scale_x)
            scaled_h = int(coords["h"] * scale_y)

            # Ensure coordinates are within image bounds
            scaled_x = max(0, min(scaled_x, current_width - 1))
            scaled_y = max(0, min(scaled_y, current_height - 1))
            scaled_w = min(scaled_w, current_width - scaled_x)
            scaled_h = min(scaled_h, current_height - scaled_y)

            # Extract region
            region = image[scaled_y:scaled_y+scaled_h, scaled_x:scaled_x+scaled_w]

            # Draw rectangle on result image
            color = get_region_color(region_type)
            cv2.rectangle(result_image, (scaled_x, scaled_y),
                         (scaled_x + scaled_w, scaled_y + scaled_h), color, 3)
            cv2.putText(result_image, region_type, (scaled_x, scaled_y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

            # Store extracted data
            extracted_data[region_type] = {
                "coordinates": {
                    "x": scaled_x, "y": scaled_y,
                    "w": scaled_w, "h": scaled_h
                },
                "scale_factors": {"x": scale_x, "y": scale_y},
                "region_size": region.shape if region.size > 0 else (0, 0)
            }

        return result_image, extracted_data

    except Exception as e:
        logger.error(f"Error applying custom regions: {str(e)}")
        return image, {}


def get_region_color(region_type: str) -> tuple:
    """Get color for region type"""
    colors = {
        "SBD_BOX": (255, 0, 0),      # Blue
        "TEST_CODE_BOX": (255, 0, 0), # Blue
        "PART_1": (0, 255, 0),       # Green
        "PART_2": (0, 0, 255),       # Red
        "PART_3": (255, 0, 255),     # Magenta
    }
    return colors.get(region_type, (128, 128, 128))  # Gray default
