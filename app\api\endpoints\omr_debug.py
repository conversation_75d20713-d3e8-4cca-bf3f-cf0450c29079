from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Dict, Any
import base64
import cv2
import numpy as np
from app.services.omr_processor import OMRProcessor
import logging

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/omr_debug",
    tags=["OMR Debug"]
)

@router.post("/viewer")
async def omr_debug_viewer(
    image_file: UploadFile = File(..., description="Answer sheet image for OMR processing")
):
    """
    API để xử lý OMR và trả về ảnh đã xử lý cùng thông tin trích xuất.
    Chỉ cần upload ảnh phiếu trả lời, không cần file Excel đáp án.
    """
    try:
        # Kiểm tra file type
        if not image_file.filename or not any(
            image_file.filename.lower().endswith(ext) 
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']
        ):
            raise HTTPException(
                status_code=400, 
                detail="Only image files (JPG, JPEG, PNG, BMP) are supported"
            )

        # Đọc nội dung ảnh
        image_content = await image_file.read()
        
        if len(image_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")

        logger.info(f"Processing OMR for image: {image_file.filename}")

        # Khởi tạo OMR processor
        omr_processor = OMRProcessor()
        
        # Chuyển đổi bytes thành image
        nparr = np.frombuffer(image_content, np.uint8)
        original_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if original_image is None:
            return {
                "success": False,
                "error": "invalid image",
                "message": "Cannot decode image file. Please check if the image is valid."
            }

        # Xử lý OMR và lấy ảnh từng bước
        processing_steps = await process_omr_with_debug_images(omr_processor, original_image)
        
        # Trích xuất thông tin
        try:
            student_info, student_answers = await omr_processor.process_image(image_content)
            
            # Tạo final report
            final_report = {
                "student_code": student_info.get('student_id', 'N/A'),
                "test_code": student_info.get('test_code', 'N/A'),
                "extracted_answers": student_answers,
                "total_answers": len(student_answers),
                "processing_status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error extracting information: {str(e)}")
            final_report = {
                "student_code": "N/A",
                "test_code": "N/A", 
                "extracted_answers": {},
                "total_answers": 0,
                "processing_status": "failed",
                "error": str(e)
            }

        return {
            "success": True,
            "filename": image_file.filename,
            "processed_images": processing_steps,
            "final_report": final_report,
            "message": "OMR processing completed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in OMR debug viewer: {str(e)}")
        return {
            "success": False,
            "error": "processing failed",
            "message": f"OMR processing failed: {str(e)}"
        }


async def process_omr_with_debug_images(omr_processor: OMRProcessor, original_image: np.ndarray) -> List[Dict[str, Any]]:
    """
    Xử lý OMR và trả về ảnh từng bước xử lý
    """
    processing_steps = []
    
    try:
        # Bước 1: Ảnh gốc
        original_b64 = image_to_base64(original_image)
        processing_steps.append({
            "step": 1,
            "name": "Original Image",
            "description": "Ảnh phiếu trả lời gốc",
            "image": original_b64
        })

        # Bước 2: Tiền xử lý (grayscale, denoise, enhance)
        preprocessed = omr_processor.preprocess_image(original_image)
        preprocessed_b64 = image_to_base64(preprocessed, is_gray=True)
        processing_steps.append({
            "step": 2,
            "name": "Preprocessed Image", 
            "description": "Ảnh sau tiền xử lý (grayscale, khử nhiễu, tăng cường độ tương phản)",
            "image": preprocessed_b64
        })

        # Bước 3: Căn chỉnh ảnh (perspective transform)
        try:
            aligned = omr_processor.align_image(preprocessed)
            aligned_b64 = image_to_base64(aligned, is_gray=True)
            processing_steps.append({
                "step": 3,
                "name": "Aligned Image",
                "description": "Ảnh sau căn chỉnh góc độ và perspective transform",
                "image": aligned_b64
            })
        except Exception as e:
            logger.warning(f"Alignment failed: {str(e)}, using preprocessed image")
            aligned = preprocessed
            processing_steps.append({
                "step": 3,
                "name": "Aligned Image (Failed)",
                "description": f"Căn chỉnh thất bại: {str(e)}, sử dụng ảnh đã tiền xử lý",
                "image": preprocessed_b64
            })

        # Bước 4: Phát hiện markers (nếu có)
        try:
            markers_image = detect_and_draw_markers(aligned)
            markers_b64 = image_to_base64(markers_image, is_gray=True)
            processing_steps.append({
                "step": 4,
                "name": "Detected Markers",
                "description": "Ảnh với các markers đã được phát hiện và đánh dấu",
                "image": markers_b64
            })
        except Exception as e:
            logger.warning(f"Marker detection failed: {str(e)}")
            processing_steps.append({
                "step": 4,
                "name": "Marker Detection (Failed)",
                "description": f"Phát hiện markers thất bại: {str(e)}",
                "image": aligned_b64
            })

        # Bước 5: Phát hiện regions/bubbles
        try:
            bubbles_image = detect_and_draw_bubbles(aligned)
            bubbles_b64 = image_to_base64(bubbles_image, is_gray=True)
            processing_steps.append({
                "step": 5,
                "name": "Detected Bubbles",
                "description": "Ảnh với các bubble câu trả lời đã được phát hiện",
                "image": bubbles_b64
            })
        except Exception as e:
            logger.warning(f"Bubble detection failed: {str(e)}")
            processing_steps.append({
                "step": 5,
                "name": "Bubble Detection (Failed)",
                "description": f"Phát hiện bubbles thất bại: {str(e)}",
                "image": aligned_b64
            })

    except Exception as e:
        logger.error(f"Error in processing steps: {str(e)}")
        # Trả về ít nhất ảnh gốc
        if not processing_steps:
            original_b64 = image_to_base64(original_image)
            processing_steps.append({
                "step": 1,
                "name": "Original Image",
                "description": "Ảnh phiếu trả lời gốc",
                "image": original_b64
            })

    return processing_steps


def image_to_base64(image: np.ndarray, is_gray: bool = False) -> str:
    """Chuyển đổi ảnh OpenCV thành base64 string"""
    try:
        if is_gray and len(image.shape) == 2:
            # Grayscale image
            _, buffer = cv2.imencode('.png', image)
        else:
            # Color image
            _, buffer = cv2.imencode('.png', image)
        
        image_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/png;base64,{image_base64}"
    except Exception as e:
        logger.error(f"Error converting image to base64: {str(e)}")
        return ""


def detect_and_draw_markers(image: np.ndarray) -> np.ndarray:
    """Phát hiện và vẽ markers trên ảnh"""
    try:
        # Tạo bản sao để vẽ
        result = image.copy()
        if len(result.shape) == 2:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)

        # Threshold ảnh để tìm contours (nếu ảnh là grayscale)
        if len(image.shape) == 2:
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        else:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Tìm contours trên ảnh binary
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Lọc contours có thể là markers (hình vuông, kích thước phù hợp)
        large_markers = []
        small_markers = []

        for contour in contours:
            area = cv2.contourArea(contour)

            # Lọc theo diện tích (marker phải có kích thước vừa phải)
            if 1000 < area < 10000:  # Large markers
                # Xấp xỉ contour thành đa giác
                epsilon = 0.04 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                # Marker phải là hình vuông (4 góc)
                if len(approx) == 4:
                    # Kiểm tra tỷ lệ khung hình gần vuông
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h

                    if 0.8 <= aspect_ratio <= 1.2:  # Gần hình vuông
                        large_markers.append(contour)

            elif 200 < area < 1000:  # Small markers
                epsilon = 0.04 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h

                    if 0.85 <= aspect_ratio <= 1.15:  # Gần hình vuông hơn cho small markers
                        small_markers.append(contour)

        # Vẽ large markers (màu xanh lá)
        cv2.drawContours(result, large_markers, -1, (0, 255, 0), 3)

        # Vẽ small markers (màu vàng)
        cv2.drawContours(result, small_markers, -1, (0, 255, 255), 2)

        # Đánh số large markers
        for i, marker in enumerate(large_markers):
            M = cv2.moments(marker)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result, f"L{i+1}", (cx-15, cy+5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Đánh số small markers
        for i, marker in enumerate(small_markers):
            M = cv2.moments(marker)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                cv2.putText(result, f"S{i+1}", (cx-10, cy+3),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

        # Thêm legend
        cv2.putText(result, f"Large Markers: {len(large_markers)}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(result, f"Small Markers: {len(small_markers)}", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return result

    except Exception as e:
        logger.error(f"Error in marker detection: {str(e)}")
        return image


def detect_and_draw_bubbles(image: np.ndarray) -> np.ndarray:
    """Phát hiện và vẽ bubbles trên ảnh"""
    try:
        # Tạo bản sao để vẽ
        result = image.copy()
        if len(result.shape) == 2:
            result = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)
        
        # Threshold ảnh để tìm contours (nếu ảnh là grayscale)
        if len(image.shape) == 2:
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        else:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Tìm contours
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Lọc contours có thể là bubbles (hình tròn, kích thước nhỏ)
        bubbles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 20 < area < 500:  # Kích thước bubble
                # Kiểm tra độ tròn
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    if circularity > 0.5:  # Khá tròn
                        bubbles.append(contour)
        
        # Vẽ bubbles
        cv2.drawContours(result, bubbles, -1, (0, 0, 255), 1)
        
        return result
        
    except Exception as e:
        logger.error(f"Error in bubble detection: {str(e)}")
        return image
