"""
Custom Region Manager Service
Quản lý các vùng trích xuất do người dùng tự định nghĩa
"""

import json
import os
import logging
from typing import Dict, Tuple, Optional
import cv2
import numpy as np

logger = logging.getLogger(__name__)

class CustomRegionManager:
    """Quản lý các vùng trích xuất tùy chỉnh"""
    
    def __init__(self, config_file: str = "custom_regions.json"):
        self.config_file = config_file
        self.regions = self.load_regions()
    
    def load_regions(self) -> Dict:
        """Load custom regions from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    regions = json.load(f)
                    logger.info(f"Loaded {len(regions)} custom regions from {self.config_file}")
                    return regions
        except Exception as e:
            logger.error(f"Error loading custom regions: {str(e)}")
        
        return {}
    
    def save_regions(self):
        """Save current regions to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.regions, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(self.regions)} custom regions to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving custom regions: {str(e)}")
            raise
    
    def add_region(self, region_type: str, coordinates: Dict, reference_dimensions: Dict):
        """Thêm vùng mới"""
        self.regions[region_type] = {
            "coordinates": coordinates,
            "reference_dimensions": reference_dimensions
        }
        self.save_regions()
        logger.info(f"Added region {region_type}: {coordinates}")
    
    def remove_region(self, region_type: str):
        """Xóa vùng"""
        if region_type in self.regions:
            del self.regions[region_type]
            self.save_regions()
            logger.info(f"Removed region {region_type}")
    
    def clear_all_regions(self):
        """Xóa tất cả các vùng"""
        self.regions.clear()
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        logger.info("Cleared all custom regions")
    
    def get_region(self, region_type: str) -> Optional[Dict]:
        """Lấy thông tin vùng"""
        return self.regions.get(region_type)
    
    def has_regions(self) -> bool:
        """Kiểm tra có vùng nào được định nghĩa không"""
        return len(self.regions) > 0
    
    def calculate_scaled_coordinates(self, region_type: str, current_width: int, current_height: int) -> Optional[Tuple[int, int, int, int]]:
        """Tính toán tọa độ đã scale cho ảnh hiện tại"""
        if region_type not in self.regions:
            return None
        
        region_info = self.regions[region_type]
        coords = region_info["coordinates"]
        ref_dims = region_info["reference_dimensions"]
        
        # Calculate scale factors
        scale_x = current_width / ref_dims["width"]
        scale_y = current_height / ref_dims["height"]
        
        # Apply scaling to coordinates
        scaled_x = int(coords["x"] * scale_x)
        scaled_y = int(coords["y"] * scale_y)
        scaled_w = int(coords["w"] * scale_x)
        scaled_h = int(coords["h"] * scale_y)
        
        # Ensure coordinates are within image bounds
        scaled_x = max(0, min(scaled_x, current_width - 1))
        scaled_y = max(0, min(scaled_y, current_height - 1))
        scaled_w = min(scaled_w, current_width - scaled_x)
        scaled_h = min(scaled_h, current_height - scaled_y)
        
        return (scaled_x, scaled_y, scaled_w, scaled_h)
    
    def extract_region(self, image: np.ndarray, region_type: str) -> Optional[np.ndarray]:
        """Trích xuất vùng từ ảnh"""
        current_height, current_width = image.shape[:2]
        coords = self.calculate_scaled_coordinates(region_type, current_width, current_height)
        
        if coords is None:
            return None
        
        x, y, w, h = coords
        if w > 0 and h > 0:
            return image[y:y+h, x:x+w]
        
        return None
    
    def extract_all_regions(self, image: np.ndarray) -> Dict[str, np.ndarray]:
        """Trích xuất tất cả các vùng từ ảnh"""
        extracted = {}
        
        for region_type in self.regions.keys():
            region = self.extract_region(image, region_type)
            if region is not None:
                extracted[region_type] = region
        
        return extracted
    
    def visualize_regions(self, image: np.ndarray) -> np.ndarray:
        """Vẽ các vùng lên ảnh để visualization"""
        try:
            # Create result image for visualization
            result_image = image.copy()
            if len(result_image.shape) == 2:
                result_image = cv2.cvtColor(result_image, cv2.COLOR_GRAY2BGR)
            
            current_height, current_width = image.shape[:2]
            
            # Define colors for different region types
            colors = {
                "SBD_BOX": (255, 0, 0),      # Blue
                "TEST_CODE_BOX": (255, 0, 0), # Blue
                "PART_1": (0, 255, 0),       # Green
                "PART_2": (0, 0, 255),       # Red
                "PART_3": (255, 0, 255),     # Magenta
            }
            
            for region_type in self.regions.keys():
                coords = self.calculate_scaled_coordinates(region_type, current_width, current_height)
                if coords is None:
                    continue
                
                x, y, w, h = coords
                color = colors.get(region_type, (128, 128, 128))  # Gray default
                
                # Draw rectangle
                cv2.rectangle(result_image, (x, y), (x + w, y + h), color, 3)
                
                # Add label
                cv2.putText(result_image, region_type, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
            
            # Add legend
            legend_y = 30
            cv2.putText(result_image, f"Custom Regions: {len(self.regions)}", 
                       (10, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            
            return result_image
            
        except Exception as e:
            logger.error(f"Error visualizing regions: {str(e)}")
            return image
    
    def get_region_info(self) -> Dict:
        """Lấy thông tin tổng quan về các vùng"""
        info = {
            "total_regions": len(self.regions),
            "region_types": list(self.regions.keys()),
            "config_file": self.config_file,
            "file_exists": os.path.exists(self.config_file)
        }
        
        for region_type, region_data in self.regions.items():
            coords = region_data["coordinates"]
            ref_dims = region_data["reference_dimensions"]
            info[f"{region_type}_info"] = {
                "coordinates": f"({coords['x']}, {coords['y']}, {coords['w']}, {coords['h']})",
                "reference_size": f"{ref_dims['width']}x{ref_dims['height']}"
            }
        
        return info
    
    def validate_regions(self, image_width: int, image_height: int) -> Dict[str, bool]:
        """Validate các vùng với kích thước ảnh hiện tại"""
        validation_results = {}
        
        for region_type in self.regions.keys():
            coords = self.calculate_scaled_coordinates(region_type, image_width, image_height)
            if coords is None:
                validation_results[region_type] = False
            else:
                x, y, w, h = coords
                # Check if region is within bounds and has reasonable size
                valid = (x >= 0 and y >= 0 and 
                        x + w <= image_width and y + h <= image_height and
                        w > 10 and h > 10)
                validation_results[region_type] = valid
        
        return validation_results


# Global instance
custom_region_manager = CustomRegionManager()
