from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import cv2
import numpy as np
import base64
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class CornerMarkerDetector:
    """Class để phát hiện marker góc và cắt ảnh"""
    
    def __init__(self):
        # Mở rộng phạm vi để debug
        self.min_marker_area = 50   # Giảm xuống để tìm marker nhỏ hơn
        self.max_marker_area = 2000 # Tăng lên để tìm marker lớn hơn
        self.target_area = 17 * 17  # 289 pixels (mục tiêu lý tưởng)
        self.debug_mode = True      # Bật chế độ debug

        # Kích thước ảnh đích sau khi cắt
        self.target_width = 540
        self.target_height = 810
        
    def detect_corner_markers(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        <PERSON><PERSON>t hiện 4 marker lớn nhất ở 4 góc của ảnh
        """
        try:
            # <PERSON><PERSON><PERSON><PERSON> sang grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Áp dụng threshold để tìm các vùng đen
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Tìm contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Lọc các contour có thể là marker
            potential_markers = []
            all_contours_info = []  # Debug: lưu thông tin tất cả contours

            for i, contour in enumerate(contours):
                # Tính diện tích
                area = cv2.contourArea(contour)

                # Debug: lưu thông tin tất cả contours
                if area > 10:  # Chỉ lưu contours có diện tích > 10
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0
                    all_contours_info.append({
                        'id': i,
                        'area': int(area),
                        'width': w,
                        'height': h,
                        'aspect_ratio': round(aspect_ratio, 2),
                        'x': x + w // 2,
                        'y': y + h // 2
                    })

                # Lọc theo diện tích (mở rộng phạm vi)
                if self.min_marker_area <= area <= self.max_marker_area:
                    # Tính bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)

                    # Kiểm tra tỷ lệ khung hình (gần vuông)
                    aspect_ratio = w / h
                    if 0.8 <= aspect_ratio <= 1.25:  # Chặt hơn cho marker vuông
                        # Tính center point
                        center_x = x + w // 2
                        center_y = y + h // 2

                        # Tính độ lệch so với diện tích mục tiêu
                        area_diff = abs(area - self.target_area)

                        potential_markers.append({
                            'x': int(center_x),
                            'y': int(center_y),
                            'area': int(area),
                            'area_diff': area_diff,
                            'width': w,
                            'height': h,
                            'aspect_ratio': round(aspect_ratio, 2),
                            'contour': contour,
                            'bbox': (x, y, w, h)
                        })
            
            # Sắp xếp theo độ gần với diện tích mục tiêu (17x17), sau đó theo diện tích
            potential_markers.sort(key=lambda m: (m['area_diff'], -m['area']))

            if len(potential_markers) < 8:
                logger.warning(f"Chỉ tìm thấy {len(potential_markers)} marker, cần ít nhất 8")
                # Nếu không đủ 8, lấy tất cả những gì có
                top_markers = potential_markers
            else:
                # Lấy 8 marker lớn nhất
                top_markers = potential_markers[:8]

            # Sắp xếp theo thứ tự từ trái qua phải, trên xuống dưới
            sorted_markers = self.sort_markers_left_to_right_top_to_bottom(top_markers)

            # Debug: log thông tin
            logger.info(f"Tìm thấy {len(contours)} contours, {len(potential_markers)} potential markers, chọn {len(sorted_markers)} markers")
            if self.debug_mode and len(all_contours_info) > 0:
                logger.info(f"Top 10 contours lớn nhất: {sorted(all_contours_info, key=lambda x: x['area'], reverse=True)[:10]}")

            # Thêm debug info vào markers
            for marker in sorted_markers:
                marker['debug_info'] = {
                    'total_contours': len(contours),
                    'potential_markers': len(potential_markers),
                    'area_range': f"{self.min_marker_area}-{self.max_marker_area}",
                    'target_area': self.target_area
                }

            return sorted_markers
            
        except Exception as e:
            logger.error(f"Error detecting corner markers: {str(e)}")
            return []

    def debug_all_contours(self, image: np.ndarray) -> tuple:
        """
        Debug: Hiển thị tất cả contours để phân tích
        """
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Vẽ tất cả contours
            debug_img = image.copy()
            all_contours_info = []

            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area > 10:  # Chỉ hiển thị contours có diện tích > 10
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0

                    # Vẽ contour
                    cv2.drawContours(debug_img, [contour], -1, (0, 255, 0), 2)
                    cv2.rectangle(debug_img, (x, y), (x + w, y + h), (255, 0, 0), 1)

                    # Vẽ thông tin
                    cv2.putText(debug_img, f"{i}:{int(area)}",
                               (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

                    all_contours_info.append({
                        'id': i,
                        'area': int(area),
                        'width': w,
                        'height': h,
                        'aspect_ratio': round(aspect_ratio, 2),
                        'x': x + w // 2,
                        'y': y + h // 2
                    })

            return debug_img, all_contours_info

        except Exception as e:
            logger.error(f"Error in debug_all_contours: {str(e)}")
            return image, []
    
    def sort_markers_left_to_right_top_to_bottom(self, markers: List[Dict]) -> List[Dict]:
        """
        Sắp xếp marker theo thứ tự từ trái qua phải, trên xuống dưới (như đọc sách)
        """
        if not markers:
            return markers

        # Sắp xếp theo y-coordinate trước (trên xuống dưới)
        # Sau đó sắp xếp theo x-coordinate (trái qua phải)
        sorted_markers = sorted(markers, key=lambda m: (m['y'], m['x']))

        return sorted_markers

    def find_corner_markers(self, markers: List[Dict]) -> List[Dict]:
        """
        Tìm 4 marker ở 4 góc từ danh sách marker
        """
        if len(markers) < 4:
            return markers

        # Chuyển đổi sang numpy array
        points = np.array([[m['x'], m['y']] for m in markers])

        # Tìm marker ở góc top-left (x+y nhỏ nhất)
        top_left_idx = np.argmin(points[:, 0] + points[:, 1])

        # Tìm marker ở góc bottom-right (x+y lớn nhất)
        bottom_right_idx = np.argmax(points[:, 0] + points[:, 1])

        # Tìm marker ở góc top-right (x-y lớn nhất)
        top_right_idx = np.argmax(points[:, 0] - points[:, 1])

        # Tìm marker ở góc bottom-left (x-y nhỏ nhất)
        bottom_left_idx = np.argmin(points[:, 0] - points[:, 1])

        return [
            markers[top_left_idx],
            markers[top_right_idx],
            markers[bottom_right_idx],
            markers[bottom_left_idx]
        ]
    
    def draw_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Vẽ marker lên ảnh để hiển thị với số thứ tự
        """
        result = image.copy()
        # Tạo nhiều màu khác nhau cho 8 marker
        colors = [
            (0, 255, 0),    # Green - M1
            (255, 0, 0),    # Red - M2
            (0, 0, 255),    # Blue - M3
            (255, 255, 0),  # Yellow - M4
            (255, 0, 255),  # Magenta - M5
            (0, 255, 255),  # Cyan - M6
            (255, 128, 0),  # Orange - M7
            (128, 0, 255),  # Purple - M8
        ]

        for i, marker in enumerate(markers):
            color = colors[i % len(colors)]

            # Vẽ circle lớn hơn tại center
            cv2.circle(result, (marker['x'], marker['y']), 15, color, -1)
            cv2.circle(result, (marker['x'], marker['y']), 18, (255, 255, 255), 3)  # White border

            # Vẽ bounding box
            x, y, w, h = marker['bbox']
            cv2.rectangle(result, (x, y), (x + w, y + h), color, 4)

            # Vẽ label với số thứ tự (M1, M2, M3, ...)
            label = f"M{i+1}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]

            # Vẽ background cho text
            cv2.rectangle(result,
                         (marker['x'] - 30, marker['y'] - 40),
                         (marker['x'] - 30 + label_size[0] + 10, marker['y'] - 40 + label_size[1] + 10),
                         (0, 0, 0), -1)

            # Vẽ text
            cv2.putText(result, label,
                       (marker['x'] - 25, marker['y'] - 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)

            # Vẽ thông tin chi tiết
            coord_text = f"({marker['x']},{marker['y']})"
            size_text = f"{marker.get('width', '?')}x{marker.get('height', '?')}"
            area_text = f"A:{marker['area']}"
            ratio_text = f"R:{marker.get('aspect_ratio', '?')}"

            cv2.putText(result, coord_text,
                       (marker['x'] - 50, marker['y'] + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            cv2.putText(result, size_text,
                       (marker['x'] - 50, marker['y'] + 42),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            cv2.putText(result, area_text,
                       (marker['x'] - 50, marker['y'] + 54),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            cv2.putText(result, ratio_text,
                       (marker['x'] - 50, marker['y'] + 66),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        return result
    
    def crop_image_by_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Cắt ảnh theo 4 marker góc sử dụng perspective transform
        Tự động chọn 4 marker ở 4 góc từ danh sách marker
        """
        if len(markers) < 4:
            raise ValueError(f"Cần ít nhất 4 marker để cắt ảnh, chỉ có {len(markers)}")

        # Tìm 4 marker ở 4 góc (top-left, top-right, bottom-right, bottom-left)
        corner_markers = self.find_corner_markers(markers)

        # Lấy tọa độ 4 góc
        src_points = np.array([
            [corner_markers[0]['x'], corner_markers[0]['y']],  # top-left
            [corner_markers[1]['x'], corner_markers[1]['y']],  # top-right
            [corner_markers[2]['x'], corner_markers[2]['y']],  # bottom-right
            [corner_markers[3]['x'], corner_markers[3]['y']]   # bottom-left
        ], dtype=np.float32)
        
        # Kích thước ảnh đích cố định
        target_width = self.target_width
        target_height = self.target_height

        # Tọa độ đích (hình chữ nhật chuẩn với kích thước cố định)
        dst_points = np.array([
            [0, 0],
            [target_width, 0],
            [target_width, target_height],
            [0, target_height]
        ], dtype=np.float32)
        
        # Tính ma trận perspective transform
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        
        # Áp dụng perspective transform với kích thước cố định
        cropped = cv2.warpPerspective(image, matrix, (target_width, target_height))
        
        return cropped

# Global instance
corner_detector = CornerMarkerDetector()

def image_to_base64(image: np.ndarray) -> str:
    """Convert OpenCV image to base64 string"""
    _, buffer = cv2.imencode('.jpg', image)
    return base64.b64encode(buffer).decode('utf-8')

def decode_image(file: UploadFile) -> np.ndarray:
    """Decode uploaded file to OpenCV image"""
    contents = file.file.read()
    nparr = np.frombuffer(contents, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise HTTPException(status_code=400, detail="Cannot decode image")
    return image

@router.post("/detect")
async def detect_corner_markers(image: UploadFile = File(...)):
    """
    Phát hiện 4 marker lớn nhất ở 4 góc của ảnh
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers
        markers = corner_detector.detect_corner_markers(img)
        
        if not markers:
            raise HTTPException(status_code=400, detail="Không tìm thấy marker nào trong ảnh")
        
        # Draw markers on image
        detected_img = corner_detector.draw_markers(img, markers)
        
        # Convert to base64
        detected_b64 = image_to_base64(detected_img)
        
        # Prepare response
        response_markers = []
        for i, marker in enumerate(markers):
            response_markers.append({
                'id': f'M{i+1}',
                'x': marker['x'],
                'y': marker['y'],
                'area': marker['area'],
                'width': marker.get('width', 0),
                'height': marker.get('height', 0),
                'aspect_ratio': marker.get('aspect_ratio', 0),
                'area_diff_from_target': marker.get('area_diff', 0),
                'target_area': self.target_area,
                'order': i + 1,
                'description': f'Marker {i+1} - {marker.get("width", "?")}x{marker.get("height", "?")} pixels'
            })
        
        return JSONResponse({
            "success": True,
            "markers": response_markers,
            "detected_image": detected_b64,
            "message": f"Đã phát hiện {len(markers)} marker"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in detect_corner_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/debug")
async def debug_all_contours(image: UploadFile = File(...)):
    """
    Debug: Hiển thị tất cả contours trong ảnh để phân tích
    """
    try:
        # Decode image
        img = decode_image(image)

        # Debug all contours
        debug_img, all_contours = corner_detector.debug_all_contours(img)

        # Convert to base64
        debug_b64 = image_to_base64(debug_img)

        # Sắp xếp contours theo diện tích
        sorted_contours = sorted(all_contours, key=lambda x: x['area'], reverse=True)

        return JSONResponse({
            "success": True,
            "debug_image": debug_b64,
            "total_contours": len(all_contours),
            "area_range_current": f"{corner_detector.min_marker_area}-{corner_detector.max_marker_area}",
            "target_area": corner_detector.target_area,
            "top_20_contours": sorted_contours[:20],
            "message": f"Tìm thấy {len(all_contours)} contours. Kiểm tra ảnh debug để xem tất cả."
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in debug_all_contours: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/crop")
async def crop_image_by_markers(image: UploadFile = File(...)):
    """
    Cắt ảnh theo 4 marker góc
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers first
        markers = corner_detector.detect_corner_markers(img)
        
        if len(markers) < 4:
            raise HTTPException(
                status_code=400,
                detail=f"Cần ít nhất 4 marker để cắt ảnh, chỉ tìm thấy {len(markers)}"
            )
        
        # Crop image using all detected markers (function will find 4 corners automatically)
        cropped_img = corner_detector.crop_image_by_markers(img, markers)
        
        # Convert to base64
        cropped_b64 = image_to_base64(cropped_img)
        
        return JSONResponse({
            "success": True,
            "cropped_image": cropped_b64,
            "original_size": {"width": img.shape[1], "height": img.shape[0]},
            "cropped_size": {"width": cropped_img.shape[1], "height": cropped_img.shape[0]},
            "target_size": {"width": corner_detector.target_width, "height": corner_detector.target_height},
            "message": f"Ảnh đã được cắt và resize về kích thước {corner_detector.target_width}x{corner_detector.target_height} pixels"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in crop_image_by_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
