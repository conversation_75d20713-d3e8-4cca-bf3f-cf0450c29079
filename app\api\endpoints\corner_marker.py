from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import cv2
import numpy as np
import base64
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class CornerMarkerDetector:
    """Class để phát hiện marker góc và cắt ảnh"""
    
    def __init__(self):
        self.min_marker_area = 100  # Diện tích tối thiểu của marker
        self.max_marker_area = 5000  # Diện tích tối đa của marker
        
    def detect_corner_markers(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Phát hiện 4 marker lớn nhất ở 4 góc của ảnh
        """
        try:
            # <PERSON><PERSON><PERSON><PERSON> sang grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Áp dụng threshold để tìm các vùng đen
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Tìm contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Lọc các contour có thể là marker
            potential_markers = []
            
            for contour in contours:
                # Tính diện tích
                area = cv2.contourArea(contour)
                
                # Lọc theo diện tích
                if self.min_marker_area < area < self.max_marker_area:
                    # Tính bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Kiểm tra tỷ lệ khung hình (gần vuông)
                    aspect_ratio = w / h
                    if 0.7 <= aspect_ratio <= 1.3:
                        # Tính center point
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        potential_markers.append({
                            'x': int(center_x),
                            'y': int(center_y),
                            'area': int(area),
                            'contour': contour,
                            'bbox': (x, y, w, h)
                        })
            
            # Sắp xếp theo diện tích giảm dần và lấy 4 marker lớn nhất
            potential_markers.sort(key=lambda m: m['area'], reverse=True)
            
            if len(potential_markers) < 4:
                logger.warning(f"Chỉ tìm thấy {len(potential_markers)} marker, cần ít nhất 4")
                return potential_markers[:4] if potential_markers else []
            
            # Lấy 4 marker lớn nhất
            top_markers = potential_markers[:4]
            
            # Sắp xếp theo vị trí góc (top-left, top-right, bottom-right, bottom-left)
            sorted_markers = self.sort_markers_by_corners(top_markers)
            
            return sorted_markers
            
        except Exception as e:
            logger.error(f"Error detecting corner markers: {str(e)}")
            return []
    
    def sort_markers_by_corners(self, markers: List[Dict]) -> List[Dict]:
        """
        Sắp xếp 4 marker theo thứ tự: top-left, top-right, bottom-right, bottom-left
        """
        if len(markers) != 4:
            return markers
        
        # Tính tổng x + y để tìm top-left (nhỏ nhất) và bottom-right (lớn nhất)
        markers_with_sum = [(m, m['x'] + m['y']) for m in markers]
        markers_with_sum.sort(key=lambda x: x[1])
        
        top_left = markers_with_sum[0][0]
        bottom_right = markers_with_sum[-1][0]
        
        # Trong 2 marker còn lại, tìm top-right và bottom-left
        remaining = [markers_with_sum[1][0], markers_with_sum[2][0]]
        
        # Top-right có x lớn hơn nhưng y nhỏ hơn
        if remaining[0]['x'] > remaining[1]['x']:
            top_right = remaining[0]
            bottom_left = remaining[1]
        else:
            top_right = remaining[1]
            bottom_left = remaining[0]
        
        return [top_left, top_right, bottom_right, bottom_left]
    
    def draw_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Vẽ marker lên ảnh để hiển thị
        """
        result = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0)]  # Green, Red, Blue, Yellow
        labels = ['TL', 'TR', 'BR', 'BL']  # Top-Left, Top-Right, Bottom-Right, Bottom-Left
        
        for i, marker in enumerate(markers):
            color = colors[i % len(colors)]
            
            # Vẽ circle tại center
            cv2.circle(result, (marker['x'], marker['y']), 10, color, -1)
            
            # Vẽ bounding box
            x, y, w, h = marker['bbox']
            cv2.rectangle(result, (x, y), (x + w, y + h), color, 3)
            
            # Vẽ label
            cv2.putText(result, f"{labels[i]}", 
                       (marker['x'] - 20, marker['y'] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        
        return result
    
    def crop_image_by_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Cắt ảnh theo 4 marker góc sử dụng perspective transform
        """
        if len(markers) != 4:
            raise ValueError("Cần đúng 4 marker để cắt ảnh")
        
        # Lấy tọa độ 4 góc
        src_points = np.array([
            [markers[0]['x'], markers[0]['y']],  # top-left
            [markers[1]['x'], markers[1]['y']],  # top-right
            [markers[2]['x'], markers[2]['y']],  # bottom-right
            [markers[3]['x'], markers[3]['y']]   # bottom-left
        ], dtype=np.float32)
        
        # Tính kích thước ảnh đích
        width = max(
            np.linalg.norm(src_points[1] - src_points[0]),  # top edge
            np.linalg.norm(src_points[2] - src_points[3])   # bottom edge
        )
        height = max(
            np.linalg.norm(src_points[3] - src_points[0]),  # left edge
            np.linalg.norm(src_points[2] - src_points[1])   # right edge
        )
        
        # Tọa độ đích (hình chữ nhật chuẩn)
        dst_points = np.array([
            [0, 0],
            [width, 0],
            [width, height],
            [0, height]
        ], dtype=np.float32)
        
        # Tính ma trận perspective transform
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        
        # Áp dụng perspective transform
        cropped = cv2.warpPerspective(image, matrix, (int(width), int(height)))
        
        return cropped

# Global instance
corner_detector = CornerMarkerDetector()

def image_to_base64(image: np.ndarray) -> str:
    """Convert OpenCV image to base64 string"""
    _, buffer = cv2.imencode('.jpg', image)
    return base64.b64encode(buffer).decode('utf-8')

def decode_image(file: UploadFile) -> np.ndarray:
    """Decode uploaded file to OpenCV image"""
    contents = file.file.read()
    nparr = np.frombuffer(contents, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise HTTPException(status_code=400, detail="Cannot decode image")
    return image

@router.post("/detect")
async def detect_corner_markers(image: UploadFile = File(...)):
    """
    Phát hiện 4 marker lớn nhất ở 4 góc của ảnh
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers
        markers = corner_detector.detect_corner_markers(img)
        
        if not markers:
            raise HTTPException(status_code=400, detail="Không tìm thấy marker nào trong ảnh")
        
        # Draw markers on image
        detected_img = corner_detector.draw_markers(img, markers)
        
        # Convert to base64
        detected_b64 = image_to_base64(detected_img)
        
        # Prepare response
        response_markers = []
        for i, marker in enumerate(markers):
            response_markers.append({
                'x': marker['x'],
                'y': marker['y'],
                'area': marker['area'],
                'corner': ['top-left', 'top-right', 'bottom-right', 'bottom-left'][i] if i < 4 else f'marker-{i+1}'
            })
        
        return JSONResponse({
            "success": True,
            "markers": response_markers,
            "detected_image": detected_b64,
            "message": f"Đã phát hiện {len(markers)} marker"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in detect_corner_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/crop")
async def crop_image_by_markers(image: UploadFile = File(...)):
    """
    Cắt ảnh theo 4 marker góc
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers first
        markers = corner_detector.detect_corner_markers(img)
        
        if len(markers) < 4:
            raise HTTPException(
                status_code=400, 
                detail=f"Cần ít nhất 4 marker để cắt ảnh, chỉ tìm thấy {len(markers)}"
            )
        
        # Crop image
        cropped_img = corner_detector.crop_image_by_markers(img, markers[:4])
        
        # Convert to base64
        cropped_b64 = image_to_base64(cropped_img)
        
        return JSONResponse({
            "success": True,
            "cropped_image": cropped_b64,
            "original_size": {"width": img.shape[1], "height": img.shape[0]},
            "cropped_size": {"width": cropped_img.shape[1], "height": cropped_img.shape[0]},
            "message": "Ảnh đã được cắt thành công"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in crop_image_by_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
