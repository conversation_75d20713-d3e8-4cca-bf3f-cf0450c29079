from fastapi import APIRouter, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import cv2
import numpy as np
import base64
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class CornerMarkerDetector:
    """Class để phát hiện marker góc và cắt ảnh"""
    
    def __init__(self):
        self.min_marker_area = 100  # Diện tích tối thiểu của marker
        self.max_marker_area = 5000  # Diện tích tối đa của marker
        
    def detect_corner_markers(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Phát hiện 4 marker lớn nhất ở 4 góc của ảnh
        """
        try:
            # <PERSON><PERSON><PERSON><PERSON> sang grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Áp dụng threshold để tìm các vùng đen
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Tìm contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Lọc các contour có thể là marker
            potential_markers = []
            
            for contour in contours:
                # Tính diện tích
                area = cv2.contourArea(contour)
                
                # Lọc theo diện tích
                if self.min_marker_area < area < self.max_marker_area:
                    # Tính bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Kiểm tra tỷ lệ khung hình (gần vuông)
                    aspect_ratio = w / h
                    if 0.7 <= aspect_ratio <= 1.3:
                        # Tính center point
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        potential_markers.append({
                            'x': int(center_x),
                            'y': int(center_y),
                            'area': int(area),
                            'contour': contour,
                            'bbox': (x, y, w, h)
                        })
            
            # Sắp xếp theo diện tích giảm dần và lấy 8 marker lớn nhất
            potential_markers.sort(key=lambda m: m['area'], reverse=True)

            if len(potential_markers) < 8:
                logger.warning(f"Chỉ tìm thấy {len(potential_markers)} marker, cần ít nhất 8")
                # Nếu không đủ 8, lấy tất cả những gì có
                top_markers = potential_markers
            else:
                # Lấy 8 marker lớn nhất
                top_markers = potential_markers[:8]

            # Sắp xếp theo thứ tự từ trái qua phải, trên xuống dưới
            sorted_markers = self.sort_markers_left_to_right_top_to_bottom(top_markers)
            
            return sorted_markers
            
        except Exception as e:
            logger.error(f"Error detecting corner markers: {str(e)}")
            return []
    
    def sort_markers_left_to_right_top_to_bottom(self, markers: List[Dict]) -> List[Dict]:
        """
        Sắp xếp marker theo thứ tự từ trái qua phải, trên xuống dưới (như đọc sách)
        """
        if not markers:
            return markers

        # Sắp xếp theo y-coordinate trước (trên xuống dưới)
        # Sau đó sắp xếp theo x-coordinate (trái qua phải)
        sorted_markers = sorted(markers, key=lambda m: (m['y'], m['x']))

        return sorted_markers

    def find_corner_markers(self, markers: List[Dict]) -> List[Dict]:
        """
        Tìm 4 marker ở 4 góc từ danh sách marker
        """
        if len(markers) < 4:
            return markers

        # Chuyển đổi sang numpy array
        points = np.array([[m['x'], m['y']] for m in markers])

        # Tìm marker ở góc top-left (x+y nhỏ nhất)
        top_left_idx = np.argmin(points[:, 0] + points[:, 1])

        # Tìm marker ở góc bottom-right (x+y lớn nhất)
        bottom_right_idx = np.argmax(points[:, 0] + points[:, 1])

        # Tìm marker ở góc top-right (x-y lớn nhất)
        top_right_idx = np.argmax(points[:, 0] - points[:, 1])

        # Tìm marker ở góc bottom-left (x-y nhỏ nhất)
        bottom_left_idx = np.argmin(points[:, 0] - points[:, 1])

        return [
            markers[top_left_idx],
            markers[top_right_idx],
            markers[bottom_right_idx],
            markers[bottom_left_idx]
        ]
    
    def draw_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Vẽ marker lên ảnh để hiển thị với số thứ tự
        """
        result = image.copy()
        # Tạo nhiều màu khác nhau cho 8 marker
        colors = [
            (0, 255, 0),    # Green - M1
            (255, 0, 0),    # Red - M2
            (0, 0, 255),    # Blue - M3
            (255, 255, 0),  # Yellow - M4
            (255, 0, 255),  # Magenta - M5
            (0, 255, 255),  # Cyan - M6
            (255, 128, 0),  # Orange - M7
            (128, 0, 255),  # Purple - M8
        ]

        for i, marker in enumerate(markers):
            color = colors[i % len(colors)]

            # Vẽ circle lớn hơn tại center
            cv2.circle(result, (marker['x'], marker['y']), 15, color, -1)
            cv2.circle(result, (marker['x'], marker['y']), 18, (255, 255, 255), 3)  # White border

            # Vẽ bounding box
            x, y, w, h = marker['bbox']
            cv2.rectangle(result, (x, y), (x + w, y + h), color, 4)

            # Vẽ label với số thứ tự (M1, M2, M3, ...)
            label = f"M{i+1}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 3)[0]

            # Vẽ background cho text
            cv2.rectangle(result,
                         (marker['x'] - 30, marker['y'] - 40),
                         (marker['x'] - 30 + label_size[0] + 10, marker['y'] - 40 + label_size[1] + 10),
                         (0, 0, 0), -1)

            # Vẽ text
            cv2.putText(result, label,
                       (marker['x'] - 25, marker['y'] - 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)

            # Vẽ tọa độ và diện tích
            coord_text = f"({marker['x']},{marker['y']})"
            area_text = f"A:{marker['area']}"
            cv2.putText(result, coord_text,
                       (marker['x'] - 40, marker['y'] + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            cv2.putText(result, area_text,
                       (marker['x'] - 40, marker['y'] + 45),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return result
    
    def crop_image_by_markers(self, image: np.ndarray, markers: List[Dict]) -> np.ndarray:
        """
        Cắt ảnh theo 4 marker góc sử dụng perspective transform
        Tự động chọn 4 marker ở 4 góc từ danh sách marker
        """
        if len(markers) < 4:
            raise ValueError(f"Cần ít nhất 4 marker để cắt ảnh, chỉ có {len(markers)}")

        # Tìm 4 marker ở 4 góc (top-left, top-right, bottom-right, bottom-left)
        corner_markers = self.find_corner_markers(markers)

        # Lấy tọa độ 4 góc
        src_points = np.array([
            [corner_markers[0]['x'], corner_markers[0]['y']],  # top-left
            [corner_markers[1]['x'], corner_markers[1]['y']],  # top-right
            [corner_markers[2]['x'], corner_markers[2]['y']],  # bottom-right
            [corner_markers[3]['x'], corner_markers[3]['y']]   # bottom-left
        ], dtype=np.float32)
        
        # Tính kích thước ảnh đích
        width = max(
            np.linalg.norm(src_points[1] - src_points[0]),  # top edge
            np.linalg.norm(src_points[2] - src_points[3])   # bottom edge
        )
        height = max(
            np.linalg.norm(src_points[3] - src_points[0]),  # left edge
            np.linalg.norm(src_points[2] - src_points[1])   # right edge
        )
        
        # Tọa độ đích (hình chữ nhật chuẩn)
        dst_points = np.array([
            [0, 0],
            [width, 0],
            [width, height],
            [0, height]
        ], dtype=np.float32)
        
        # Tính ma trận perspective transform
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        
        # Áp dụng perspective transform
        cropped = cv2.warpPerspective(image, matrix, (int(width), int(height)))
        
        return cropped

# Global instance
corner_detector = CornerMarkerDetector()

def image_to_base64(image: np.ndarray) -> str:
    """Convert OpenCV image to base64 string"""
    _, buffer = cv2.imencode('.jpg', image)
    return base64.b64encode(buffer).decode('utf-8')

def decode_image(file: UploadFile) -> np.ndarray:
    """Decode uploaded file to OpenCV image"""
    contents = file.file.read()
    nparr = np.frombuffer(contents, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise HTTPException(status_code=400, detail="Cannot decode image")
    return image

@router.post("/detect")
async def detect_corner_markers(image: UploadFile = File(...)):
    """
    Phát hiện 4 marker lớn nhất ở 4 góc của ảnh
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers
        markers = corner_detector.detect_corner_markers(img)
        
        if not markers:
            raise HTTPException(status_code=400, detail="Không tìm thấy marker nào trong ảnh")
        
        # Draw markers on image
        detected_img = corner_detector.draw_markers(img, markers)
        
        # Convert to base64
        detected_b64 = image_to_base64(detected_img)
        
        # Prepare response
        response_markers = []
        for i, marker in enumerate(markers):
            response_markers.append({
                'id': f'M{i+1}',
                'x': marker['x'],
                'y': marker['y'],
                'area': marker['area'],
                'order': i + 1,
                'description': f'Marker {i+1} (từ trái qua phải, trên xuống dưới)'
            })
        
        return JSONResponse({
            "success": True,
            "markers": response_markers,
            "detected_image": detected_b64,
            "message": f"Đã phát hiện {len(markers)} marker"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in detect_corner_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/crop")
async def crop_image_by_markers(image: UploadFile = File(...)):
    """
    Cắt ảnh theo 4 marker góc
    """
    try:
        # Decode image
        img = decode_image(image)
        
        # Detect markers first
        markers = corner_detector.detect_corner_markers(img)
        
        if len(markers) < 4:
            raise HTTPException(
                status_code=400,
                detail=f"Cần ít nhất 4 marker để cắt ảnh, chỉ tìm thấy {len(markers)}"
            )
        
        # Crop image using all detected markers (function will find 4 corners automatically)
        cropped_img = corner_detector.crop_image_by_markers(img, markers)
        
        # Convert to base64
        cropped_b64 = image_to_base64(cropped_img)
        
        return JSONResponse({
            "success": True,
            "cropped_image": cropped_b64,
            "original_size": {"width": img.shape[1], "height": img.shape[0]},
            "cropped_size": {"width": cropped_img.shape[1], "height": cropped_img.shape[0]},
            "message": "Ảnh đã được cắt thành công"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in crop_image_by_markers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
