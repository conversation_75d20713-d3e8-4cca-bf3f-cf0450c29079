// Region Selector JavaScript
class RegionSelector {
    constructor() {
        this.currentRegion = null;
        this.isSelecting = false;
        this.startX = 0;
        this.startY = 0;
        this.imageScale = 1;
        this.originalImageWidth = 0;
        this.originalImageHeight = 0;
        this.regions = {};
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSavedRegions();
    }

    initializeElements() {
        this.fileUpload = document.getElementById('fileUpload');
        this.imageInput = document.getElementById('imageInput');
        this.selectorSection = document.getElementById('selectorSection');
        this.imageContainer = document.getElementById('imageContainer');
        this.selectorImage = document.getElementById('selectorImage');
        this.selectionOverlay = document.getElementById('selectionOverlay');
        this.statusMessage = document.getElementById('statusMessage');
        
        // Coordinate displays
        this.coordX = document.getElementById('coordX');
        this.coordY = document.getElementById('coordY');
        this.coordW = document.getElementById('coordW');
        this.coordH = document.getElementById('coordH');
        
        // Buttons
        this.regionButtons = document.querySelectorAll('.region-button');
        this.saveButton = document.getElementById('saveRegion');
        this.resetButton = document.getElementById('resetRegions');
        this.testButton = document.getElementById('testRegions');
    }

    setupEventListeners() {
        // File upload
        this.fileUpload.addEventListener('click', () => this.imageInput.click());
        this.imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
        
        // Drag and drop
        this.fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.fileUpload.classList.add('dragover');
        });
        
        this.fileUpload.addEventListener('dragleave', () => {
            this.fileUpload.classList.remove('dragover');
        });
        
        this.fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            this.fileUpload.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.loadImage(files[0]);
            }
        });

        // Region selection buttons
        this.regionButtons.forEach(button => {
            button.addEventListener('click', () => this.selectRegionType(button.dataset.region));
        });

        // Image selection events
        this.selectorImage.addEventListener('mousedown', (e) => this.startSelection(e));
        this.selectorImage.addEventListener('mousemove', (e) => this.updateSelection(e));
        this.selectorImage.addEventListener('mouseup', (e) => this.endSelection(e));

        // Action buttons
        this.saveButton.addEventListener('click', () => this.saveCurrentRegion());
        this.resetButton.addEventListener('click', () => this.resetAllRegions());
        this.testButton.addEventListener('click', () => this.testRegions());
    }

    handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            this.loadImage(file);
        }
    }

    loadImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.selectorImage.src = e.target.result;
            this.selectorImage.onload = () => {
                this.originalImageWidth = this.selectorImage.naturalWidth;
                this.originalImageHeight = this.selectorImage.naturalHeight;
                this.calculateImageScale();
                this.selectorSection.style.display = 'block';
                this.showStatus('Ảnh đã được tải. Chọn loại vùng và kéo chuột để định nghĩa vùng.', 'success');
            };
        };
        reader.readAsDataURL(file);
    }

    calculateImageScale() {
        const displayedWidth = this.selectorImage.offsetWidth;
        const displayedHeight = this.selectorImage.offsetHeight;
        this.imageScale = this.originalImageWidth / displayedWidth;
    }

    selectRegionType(regionType) {
        this.currentRegion = regionType;
        
        // Update button states
        this.regionButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-region="${regionType}"]`).classList.add('active');
        
        // Load existing region if available
        if (this.regions[regionType]) {
            this.displayRegion(this.regions[regionType]);
        } else {
            this.hideSelection();
        }
        
        this.showStatus(`Đã chọn vùng: ${this.getRegionName(regionType)}. Kéo chuột trên ảnh để định nghĩa vùng.`, 'success');
    }

    getRegionName(regionType) {
        const names = {
            'SBD_BOX': 'Số báo danh',
            'TEST_CODE_BOX': 'Mã đề thi',
            'PART_1': 'Part I (40 câu trắc nghiệm)',
            'PART_2': 'Part II (8 câu tự luận)',
            'PART_3': 'Part III (6 câu điền số)'
        };
        return names[regionType] || regionType;
    }

    startSelection(event) {
        if (!this.currentRegion) {
            this.showStatus('Vui lòng chọn loại vùng trước khi định nghĩa.', 'error');
            return;
        }

        this.isSelecting = true;
        const rect = this.selectorImage.getBoundingClientRect();
        this.startX = event.clientX - rect.left;
        this.startY = event.clientY - rect.top;
        
        this.selectionOverlay.style.left = this.startX + 'px';
        this.selectionOverlay.style.top = this.startY + 'px';
        this.selectionOverlay.style.width = '0px';
        this.selectionOverlay.style.height = '0px';
        this.selectionOverlay.style.display = 'block';
    }

    updateSelection(event) {
        if (!this.isSelecting) return;

        const rect = this.selectorImage.getBoundingClientRect();
        const currentX = event.clientX - rect.left;
        const currentY = event.clientY - rect.top;

        const width = Math.abs(currentX - this.startX);
        const height = Math.abs(currentY - this.startY);
        const left = Math.min(currentX, this.startX);
        const top = Math.min(currentY, this.startY);

        this.selectionOverlay.style.left = left + 'px';
        this.selectionOverlay.style.top = top + 'px';
        this.selectionOverlay.style.width = width + 'px';
        this.selectionOverlay.style.height = height + 'px';

        // Update coordinates display (convert to original image coordinates)
        const originalX = Math.round(left * this.imageScale);
        const originalY = Math.round(top * this.imageScale);
        const originalW = Math.round(width * this.imageScale);
        const originalH = Math.round(height * this.imageScale);

        this.updateCoordinatesDisplay(originalX, originalY, originalW, originalH);
    }

    endSelection(event) {
        if (!this.isSelecting) return;
        
        this.isSelecting = false;
        
        // Save the current selection
        const rect = this.selectorImage.getBoundingClientRect();
        const currentX = event.clientX - rect.left;
        const currentY = event.clientY - rect.top;

        const width = Math.abs(currentX - this.startX);
        const height = Math.abs(currentY - this.startY);
        const left = Math.min(currentX, this.startX);
        const top = Math.min(currentY, this.startY);

        // Convert to original image coordinates
        const originalX = Math.round(left * this.imageScale);
        const originalY = Math.round(top * this.imageScale);
        const originalW = Math.round(width * this.imageScale);
        const originalH = Math.round(height * this.imageScale);

        if (originalW > 10 && originalH > 10) { // Minimum size check
            this.regions[this.currentRegion] = {
                x: originalX,
                y: originalY,
                w: originalW,
                h: originalH
            };
            this.showStatus(`Vùng ${this.getRegionName(this.currentRegion)} đã được định nghĩa.`, 'success');
        } else {
            this.showStatus('Vùng quá nhỏ. Vui lòng chọn vùng lớn hơn.', 'error');
            this.hideSelection();
        }
    }

    displayRegion(region) {
        // Convert original coordinates to display coordinates
        const displayX = region.x / this.imageScale;
        const displayY = region.y / this.imageScale;
        const displayW = region.w / this.imageScale;
        const displayH = region.h / this.imageScale;

        this.selectionOverlay.style.left = displayX + 'px';
        this.selectionOverlay.style.top = displayY + 'px';
        this.selectionOverlay.style.width = displayW + 'px';
        this.selectionOverlay.style.height = displayH + 'px';
        this.selectionOverlay.style.display = 'block';

        this.updateCoordinatesDisplay(region.x, region.y, region.w, region.h);
    }

    hideSelection() {
        this.selectionOverlay.style.display = 'none';
        this.updateCoordinatesDisplay(0, 0, 0, 0);
    }

    updateCoordinatesDisplay(x, y, w, h) {
        this.coordX.textContent = x;
        this.coordY.textContent = y;
        this.coordW.textContent = w;
        this.coordH.textContent = h;
    }

    async saveCurrentRegion() {
        if (!this.currentRegion || !this.regions[this.currentRegion]) {
            this.showStatus('Không có vùng nào để lưu. Vui lòng định nghĩa vùng trước.', 'error');
            return;
        }

        try {
            const response = await fetch('/api/v1/omr_debug/save_region', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    region_type: this.currentRegion,
                    coordinates: this.regions[this.currentRegion],
                    image_dimensions: {
                        width: this.originalImageWidth,
                        height: this.originalImageHeight
                    }
                })
            });

            const result = await response.json();
            if (result.success) {
                this.showStatus(`Vùng ${this.getRegionName(this.currentRegion)} đã được lưu thành công!`, 'success');
                this.saveToLocalStorage();
            } else {
                this.showStatus(`Lỗi khi lưu: ${result.message}`, 'error');
            }
        } catch (error) {
            this.showStatus(`Lỗi kết nối: ${error.message}`, 'error');
        }
    }

    async resetAllRegions() {
        if (confirm('Bạn có chắc muốn xóa tất cả các vùng đã định nghĩa?')) {
            this.regions = {};
            this.hideSelection();
            this.currentRegion = null;
            this.regionButtons.forEach(btn => btn.classList.remove('active'));
            
            try {
                await fetch('/api/v1/omr_debug/reset_regions', { method: 'POST' });
                this.showStatus('Đã xóa tất cả các vùng.', 'success');
                localStorage.removeItem('omr_regions');
            } catch (error) {
                this.showStatus(`Lỗi khi reset: ${error.message}`, 'error');
            }
        }
    }

    async testRegions() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('image', file);
                
                try {
                    const response = await fetch('/api/v1/omr_debug/test_regions', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        // Open result in new window
                        const newWindow = window.open('', '_blank');
                        newWindow.document.write(`
                            <html>
                                <head><title>Test Result</title></head>
                                <body>
                                    <h2>Kết quả test vùng trích xuất</h2>
                                    <img src="${result.test_image}" style="max-width: 100%; height: auto;">
                                    <pre>${JSON.stringify(result.extracted_data, null, 2)}</pre>
                                </body>
                            </html>
                        `);
                        this.showStatus('Test thành công! Kết quả hiển thị trong tab mới.', 'success');
                    } else {
                        this.showStatus(`Test thất bại: ${result.message}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Lỗi khi test: ${error.message}`, 'error');
                }
            }
        };
        input.click();
    }

    saveToLocalStorage() {
        localStorage.setItem('omr_regions', JSON.stringify(this.regions));
    }

    loadSavedRegions() {
        const saved = localStorage.getItem('omr_regions');
        if (saved) {
            this.regions = JSON.parse(saved);
        }
    }

    showStatus(message, type) {
        this.statusMessage.textContent = message;
        this.statusMessage.className = `status-message status-${type}`;
        this.statusMessage.style.display = 'block';
        
        setTimeout(() => {
            this.statusMessage.style.display = 'none';
        }, 5000);
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new RegionSelector();
});
