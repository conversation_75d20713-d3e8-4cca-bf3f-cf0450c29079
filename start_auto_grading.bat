@echo off
echo ========================================
echo    PlanBook AI - Auto Grading System
echo ========================================
echo.

echo [1/3] Checking Python environment...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo.
echo [2/3] Installing/Updating dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo [3/3] Starting FastAPI server...
echo.
echo ========================================
echo  Server will start at: http://localhost:8000
echo  Auto Grading Interface: http://localhost:8000/auto-grading
echo  API Documentation: http://localhost:8000/api/v1/docs
echo ========================================
echo.
echo Press Ctrl+C to stop the server
echo.

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
