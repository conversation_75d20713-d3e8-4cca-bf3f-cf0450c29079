"""
Region Extractor - Cắt và xử lý các vùng từ ảnh OMR đã chuẩn hóa
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Any
import logging

from app.constants.omr_regions import (
    STUDENT_INFO_REGION, ANSWER_REGIONS, BUBBLE_CONFIG, 
    DIGIT_CONFIG, PART1_CONFIG, PART2_CONFIG, PART3_CONFIG,
    get_region_coordinates
)

logger = logging.getLogger(__name__)


class RegionExtractor:
    """Trích xuất và xử lý các vùng từ ảnh OMR đã chuẩn hóa"""
    
    def __init__(self):
        self.fill_threshold = BUBBLE_CONFIG["fill_threshold"]
        
    def extract_all_regions(self, warped_image: np.ndarray) -> Dict[str, Any]:
        """
        Trích xuất tất cả các vùng từ ảnh đã chuẩn hóa
        
        Args:
            warped_image: Ảnh đã qua perspective transform
            
        Returns:
            Dict chứa thông tin đã trích xuất
        """
        results = {
            "student_info": {},
            "answers": {},
            "processing_status": "success",
            "errors": []
        }
        
        try:
            # Trích xuất thông tin sinh viên
            results["student_info"] = self.extract_student_info(warped_image)
            
            # Trích xuất câu trả lời
            results["answers"] = self.extract_all_answers(warped_image)
            
        except Exception as e:
            logger.error(f"Error extracting regions: {str(e)}")
            results["processing_status"] = "failed"
            results["errors"].append(str(e))
            
        return results
    
    def extract_student_info(self, warped_image: np.ndarray) -> Dict[str, str]:
        """Trích xuất thông tin sinh viên (SBD, mã đề)"""
        student_info = {}
        
        try:
            # Trích xuất SBD
            sbd_box = STUDENT_INFO_REGION["SBD_BOX"]
            sbd_region = self.crop_region(warped_image, sbd_box)
            student_info["student_id"] = self.extract_digit_sequence(
                sbd_region, DIGIT_CONFIG["sbd_digits"]
            )
            
            # Trích xuất mã đề
            test_code_box = STUDENT_INFO_REGION["TEST_CODE_BOX"]
            test_code_region = self.crop_region(warped_image, test_code_box)
            student_info["test_code"] = self.extract_digit_sequence(
                test_code_region, DIGIT_CONFIG["test_code_digits"]
            )
            
        except Exception as e:
            logger.error(f"Error extracting student info: {str(e)}")
            student_info = {"student_id": "N/A", "test_code": "N/A"}
            
        return student_info
    
    def extract_all_answers(self, warped_image: np.ndarray) -> Dict[int, str]:
        """Trích xuất tất cả câu trả lời"""
        all_answers = {}
        
        try:
            # Part I: Trắc nghiệm 40 câu
            part1_answers = self.extract_part1_answers(warped_image)
            all_answers.update(part1_answers)
            
            # Part II: Tự luận dạng trắc nghiệm
            part2_answers = self.extract_part2_answers(warped_image)
            all_answers.update(part2_answers)
            
            # Part III: Điền số
            part3_answers = self.extract_part3_answers(warped_image)
            all_answers.update(part3_answers)
            
        except Exception as e:
            logger.error(f"Error extracting answers: {str(e)}")
            
        return all_answers
    
    def extract_part1_answers(self, warped_image: np.ndarray) -> Dict[int, str]:
        """Trích xuất câu trả lời Part I (40 câu trắc nghiệm)"""
        answers = {}
        
        try:
            part1_config = ANSWER_REGIONS["PART_1"]
            columns = part1_config["columns"]
            
            for col_idx, col_box in enumerate(columns):
                col_region = self.crop_region(warped_image, col_box)
                
                # Chia cột thành 10 hàng
                col_height = col_box[3]
                row_height = col_height // 10
                
                for row_idx in range(10):
                    question_num = col_idx * 10 + row_idx + 1
                    
                    # Cắt vùng câu hỏi
                    y_start = row_idx * row_height
                    y_end = (row_idx + 1) * row_height
                    question_region = col_region[y_start:y_end, :]
                    
                    # Trích xuất đáp án (A, B, C, D)
                    answer = self.extract_multiple_choice_answer(question_region)
                    if answer:
                        answers[question_num] = answer
                        
        except Exception as e:
            logger.error(f"Error extracting Part I answers: {str(e)}")
            
        return answers
    
    def extract_part2_answers(self, warped_image: np.ndarray) -> Dict[int, str]:
        """Trích xuất câu trả lời Part II (8 câu tự luận dạng trắc nghiệm)"""
        answers = {}
        
        try:
            part2_config = ANSWER_REGIONS["PART_2"]
            rows = part2_config["rows"]
            
            for row_idx, row_box in enumerate(rows):
                question_num = 41 + row_idx  # Câu 41-48
                row_region = self.crop_region(warped_image, row_box)
                
                # Trích xuất đáp án (a, b, c, d)
                answer = self.extract_multiple_choice_answer(
                    row_region, options=PART2_CONFIG["sub_option_labels"]
                )
                if answer:
                    answers[question_num] = answer
                    
        except Exception as e:
            logger.error(f"Error extracting Part II answers: {str(e)}")
            
        return answers
    
    def extract_part3_answers(self, warped_image: np.ndarray) -> Dict[int, str]:
        """Trích xuất câu trả lời Part III (6 câu điền số)"""
        answers = {}
        
        try:
            part3_config = ANSWER_REGIONS["PART_3"]
            questions = part3_config["questions"]
            
            for q_idx, q_box in enumerate(questions):
                question_num = 49 + q_idx  # Câu 49-54
                question_region = self.crop_region(warped_image, q_box)
                
                # Trích xuất 4 chữ số
                digit_sequence = self.extract_digit_sequence(
                    question_region, PART3_CONFIG["digits_per_question"]
                )
                if digit_sequence and digit_sequence != "0000":
                    answers[question_num] = digit_sequence
                    
        except Exception as e:
            logger.error(f"Error extracting Part III answers: {str(e)}")
            
        return answers
    
    def crop_region(self, image: np.ndarray, box: Tuple[int, int, int, int]) -> np.ndarray:
        """Cắt vùng từ ảnh theo tọa độ"""
        x, y, w, h = box
        return image[y:y+h, x:x+w]
    
    def extract_multiple_choice_answer(self, region: np.ndarray, options: List[str] = None) -> str:
        """Trích xuất đáp án trắc nghiệm từ một vùng"""
        if options is None:
            options = PART1_CONFIG["option_labels"]  # ["A", "B", "C", "D"]
            
        try:
            # Chia vùng thành các ô tương ứng với số lựa chọn
            region_width = region.shape[1]
            option_width = region_width // len(options)
            
            for i, option in enumerate(options):
                # Cắt ô của từng lựa chọn
                x_start = i * option_width
                x_end = (i + 1) * option_width
                option_region = region[:, x_start:x_end]
                
                # Kiểm tra xem ô có được tô không
                if self.is_region_filled(option_region):
                    return option
                    
        except Exception as e:
            logger.error(f"Error extracting multiple choice: {str(e)}")
            
        return ""
    
    def extract_digit_sequence(self, region: np.ndarray, num_digits: int) -> str:
        """Trích xuất chuỗi số từ vùng bubble digits"""
        try:
            # Chia vùng thành các cột (mỗi cột là 1 chữ số)
            region_width = region.shape[1]
            digit_width = region_width // num_digits
            
            result = ""
            for digit_idx in range(num_digits):
                # Cắt cột của chữ số
                x_start = digit_idx * digit_width
                x_end = (digit_idx + 1) * digit_width
                digit_column = region[:, x_start:x_end]
                
                # Chia cột thành 10 hàng (0-9)
                digit_height = digit_column.shape[0]
                row_height = digit_height // 10
                
                selected_digit = "0"  # Mặc định
                for row_idx in range(10):
                    y_start = row_idx * row_height
                    y_end = (row_idx + 1) * row_height
                    digit_cell = digit_column[y_start:y_end, :]
                    
                    if self.is_region_filled(digit_cell):
                        selected_digit = str(row_idx)
                        break
                
                result += selected_digit
                
            return result
            
        except Exception as e:
            logger.error(f"Error extracting digit sequence: {str(e)}")
            return "0" * num_digits
    
    def is_region_filled(self, region: np.ndarray) -> bool:
        """Kiểm tra vùng có được tô không dựa trên tỷ lệ pixel đen"""
        try:
            # Threshold vùng để có ảnh binary
            if len(region.shape) == 3:
                gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            else:
                gray_region = region
                
            _, binary = cv2.threshold(gray_region, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Tính tỷ lệ pixel đen
            total_pixels = binary.size
            black_pixels = cv2.countNonZero(binary)
            fill_ratio = black_pixels / total_pixels
            
            return fill_ratio > self.fill_threshold
            
        except Exception as e:
            logger.error(f"Error checking if region is filled: {str(e)}")
            return False
