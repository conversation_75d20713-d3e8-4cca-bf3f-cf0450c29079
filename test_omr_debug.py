import requests
import os

def test_omr_debug_api():
    """Test OMR debug API"""
    
    # URL API
    api_url = "http://localhost:8000/api/v1/omr_debug/viewer"
    
    print("🚀 Testing OMR Debug API...")
    
    # Tìm ảnh test trong thư mục data/
    image_paths = []
    if os.path.exists("data"):
        for file in os.listdir("data"):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_paths.append(os.path.join("data", file))
    
    if not image_paths:
        print("⚠️ Không tìm thấy ảnh test trong thư mục data/")
        print("💡 Vui lòng thêm ảnh phiếu trả lời vào thư mục data/ để test")
        
        # Test endpoint health với file giả
        try:
            response = requests.post(api_url)
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code == 422:
                print("✅ API endpoint hoạt động (cần thêm image_file)")
                print("📋 Response:", response.json())
                return True
            else:
                print("❌ API response không như mong đợi")
                print("📋 Response:", response.text)
                return False
                
        except Exception as e:
            print(f"❌ Lỗi kết nối API: {e}")
            return False
    
    # Test với ảnh thật
    image_path = image_paths[0]
    print(f"📸 Sử dụng ảnh test: {image_path}")
    
    try:
        # Chuẩn bị files để upload
        with open(image_path, 'rb') as f:
            files = {
                'image_file': (os.path.basename(image_path), f, 'image/jpeg')
            }
            
            print("📤 Đang gửi request...")
            response = requests.post(api_url, files=files, timeout=60)
            
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API test thành công!")
            print(f"📋 Success: {result.get('success')}")
            print(f"📋 Message: {result.get('message')}")
            print(f"📋 Filename: {result.get('filename')}")
            
            # Kiểm tra processed images
            processed_images = result.get('processed_images', [])
            print(f"📸 Số ảnh đã xử lý: {len(processed_images)}")
            
            for img in processed_images:
                print(f"  - Bước {img.get('step')}: {img.get('name')}")
            
            # Kiểm tra final report
            final_report = result.get('final_report', {})
            print(f"📊 Student Code: {final_report.get('student_code')}")
            print(f"📊 Test Code: {final_report.get('test_code')}")
            print(f"📊 Total Answers: {final_report.get('total_answers')}")
            print(f"📊 Status: {final_report.get('processing_status')}")
            
            return True
            
        else:
            print("❌ API test thất bại!")
            print("📋 Response:", response.text)
            return False
            
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {e}")
        return False

if __name__ == "__main__":
    success = test_omr_debug_api()
    if success:
        print("\n🎉 Test hoàn tất thành công!")
    else:
        print("\n💥 Test thất bại!")
