#!/usr/bin/env python3
"""
Script để tạo file Excel mẫu cho hệ thống chấm điểm tự động
"""

import pandas as pd
import os

def create_sample_answer_key():
    """Tạo file Excel mẫu với đáp án cho 3 mã đề"""
    
    # Tạo dữ liệu mẫu
    data = {
        'test_code': ['001', '002', '003'],
        'question_1': ['A', 'B', 'C'],
        'question_2': ['B', 'A', 'D'],
        'question_3': ['C', 'D', 'A'],
        'question_4': ['D', 'C', 'B'],
        'question_5': ['A', 'B', 'C'],
        'question_6': ['B', 'A', 'D'],
        'question_7': ['C', 'D', 'A'],
        'question_8': ['D', 'C', 'B'],
        'question_9': ['A', 'B', 'C'],
        'question_10': ['B', 'A', 'D'],
        'question_11': ['C', 'D', 'A'],
        'question_12': ['D', 'C', 'B'],
        'question_13': ['A', 'B', 'C'],
        'question_14': ['B', 'A', 'D'],
        'question_15': ['C', 'D', 'A'],
        'question_16': ['D', 'C', 'B'],
        'question_17': ['A', 'B', 'C'],
        'question_18': ['B', 'A', 'D'],
        'question_19': ['C', 'D', 'A'],
        'question_20': ['D', 'C', 'B'],
        'question_21': ['A', 'B', 'C'],
        'question_22': ['B', 'A', 'D'],
        'question_23': ['C', 'D', 'A'],
        'question_24': ['D', 'C', 'B'],
        'question_25': ['A', 'B', 'C'],
        'question_26': ['B', 'A', 'D'],
        'question_27': ['C', 'D', 'A'],
        'question_28': ['D', 'C', 'B'],
        'question_29': ['A', 'B', 'C'],
        'question_30': ['B', 'A', 'D'],
        'question_31': ['C', 'D', 'A'],
        'question_32': ['D', 'C', 'B'],
        'question_33': ['A', 'B', 'C'],
        'question_34': ['B', 'A', 'D'],
        'question_35': ['C', 'D', 'A'],
        'question_36': ['D', 'C', 'B'],
        'question_37': ['A', 'B', 'C'],
        'question_38': ['B', 'A', 'D'],
        'question_39': ['C', 'D', 'A'],
        'question_40': ['D', 'C', 'B'],
    }
    
    # Tạo DataFrame
    df = pd.DataFrame(data)
    
    # Tạo thư mục nếu chưa có
    os.makedirs('static/demo_data', exist_ok=True)
    
    # Lưu file Excel
    output_file = 'static/demo_data/sample_answer_key.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Đã tạo file Excel mẫu: {output_file}")
    print(f"📊 File chứa {len(df)} mã đề với {len(df.columns)-1} câu hỏi mỗi đề")
    
    # In preview
    print("\n📋 Preview dữ liệu:")
    print(df.head())
    
    return output_file

def create_detailed_sample():
    """Tạo file Excel mẫu chi tiết hơn với nhiều mã đề"""
    
    import random
    
    # Tạo 10 mã đề
    test_codes = [f"{i:03d}" for i in range(1, 11)]
    
    # Tạo đáp án ngẫu nhiên cho 50 câu hỏi
    data = {'test_code': test_codes}
    
    choices = ['A', 'B', 'C', 'D']
    
    for i in range(1, 51):  # 50 câu hỏi
        question_key = f'question_{i}'
        # Tạo đáp án ngẫu nhiên cho mỗi mã đề
        data[question_key] = [random.choice(choices) for _ in test_codes]
    
    # Tạo DataFrame
    df = pd.DataFrame(data)
    
    # Lưu file Excel
    output_file = 'static/demo_data/detailed_answer_key.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Đã tạo file Excel chi tiết: {output_file}")
    print(f"📊 File chứa {len(df)} mã đề với {len(df.columns)-1} câu hỏi mỗi đề")
    
    return output_file

if __name__ == "__main__":
    print("🚀 Tạo file Excel mẫu cho hệ thống chấm điểm tự động")
    print("=" * 60)
    
    # Tạo file mẫu cơ bản
    basic_file = create_sample_answer_key()
    
    print("\n" + "=" * 60)
    
    # Tạo file mẫu chi tiết
    detailed_file = create_detailed_sample()
    
    print("\n" + "=" * 60)
    print("✨ Hoàn thành! Bạn có thể sử dụng các file sau để test:")
    print(f"   - File cơ bản: {basic_file}")
    print(f"   - File chi tiết: {detailed_file}")
    print("\n💡 Hướng dẫn sử dụng:")
    print("   1. Khởi động server: uvicorn app.main:app --reload")
    print("   2. Truy cập: http://localhost:8000/auto-grading")
    print("   3. Upload ảnh phiếu trả lời và file Excel vừa tạo")
