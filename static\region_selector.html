<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMR Region Selector - Chọn vùng tr<PERSON>ch xuất</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .file-upload {
            border: 3px dashed #4facfe;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }

        .file-upload.dragover {
            border-color: #00f2fe;
            background: #e6f3ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .selector-section {
            display: none;
            margin-top: 30px;
        }

        .image-container {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            max-width: 100%;
        }

        .selector-image {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .selection-overlay {
            position: absolute;
            border: 2px solid #ff4757;
            background: rgba(255, 71, 87, 0.2);
            display: none;
            pointer-events: none;
        }

        .region-controls {
            margin-top: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .region-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .region-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .region-button.active {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        }

        .coordinates-display {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .coordinates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .coordinate-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .coordinate-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }

        .coordinate-value {
            font-size: 1.1em;
            color: #333;
            margin-top: 5px;
        }

        .action-buttons {
            margin-top: 30px;
            text-align: center;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .action-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .save-button {
            background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
            color: white;
        }

        .reset-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
            color: white;
        }

        .test-button {
            background: linear-gradient(135deg, #ffa726 0%, #ffcc02 100%);
            color: white;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .instructions h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .instructions ol {
            margin-left: 20px;
            color: #424242;
        }

        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 OMR Region Selector</h1>
            <p>Chọn vùng trích xuất chính xác cho phiếu trả lời OMR</p>
        </div>

        <div class="main-content">
            <!-- Upload Section -->
            <div class="upload-section">
                <div class="file-upload" id="fileUpload">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Kéo thả ảnh phiếu trả lời vào đây hoặc click để chọn</div>
                    <button class="upload-button">Chọn ảnh</button>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>
            </div>

            <!-- Instructions -->
            <div class="instructions">
                <h3>📋 Hướng dẫn sử dụng:</h3>
                <ol>
                    <li>Upload ảnh phiếu trả lời đã được perspective transform</li>
                    <li>Chọn loại vùng muốn định nghĩa (SBD, Mã đề, Part I, II, III)</li>
                    <li>Kéo chuột trên ảnh để chọn vùng chính xác</li>
                    <li>Kiểm tra tọa độ và lưu cấu hình</li>
                    <li>Test với ảnh khác để kiểm tra tỷ lệ</li>
                </ol>
            </div>

            <!-- Selector Section -->
            <div class="selector-section" id="selectorSection">
                <div class="region-controls">
                    <button class="region-button" data-region="SBD_BOX">📝 Vùng SBD</button>
                    <button class="region-button" data-region="TEST_CODE_BOX">🔢 Mã đề thi</button>
                    <button class="region-button" data-region="PART_1">📊 Part I (40 câu)</button>
                    <button class="region-button" data-region="PART_2">📋 Part II (8 câu)</button>
                    <button class="region-button" data-region="PART_3">🔢 Part III (6 câu)</button>
                </div>

                <div class="image-container" id="imageContainer">
                    <img id="selectorImage" class="selector-image" alt="OMR Image">
                    <div class="selection-overlay" id="selectionOverlay"></div>
                </div>

                <div class="coordinates-display">
                    <h3>📐 Tọa độ vùng đã chọn:</h3>
                    <div class="coordinates-grid">
                        <div class="coordinate-item">
                            <div class="coordinate-label">X (Left)</div>
                            <div class="coordinate-value" id="coordX">0</div>
                        </div>
                        <div class="coordinate-item">
                            <div class="coordinate-label">Y (Top)</div>
                            <div class="coordinate-value" id="coordY">0</div>
                        </div>
                        <div class="coordinate-item">
                            <div class="coordinate-label">Width</div>
                            <div class="coordinate-value" id="coordW">0</div>
                        </div>
                        <div class="coordinate-item">
                            <div class="coordinate-label">Height</div>
                            <div class="coordinate-value" id="coordH">0</div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="action-button save-button" id="saveRegion">💾 Lưu vùng</button>
                    <button class="action-button reset-button" id="resetRegions">🔄 Reset tất cả</button>
                    <button class="action-button test-button" id="testRegions">🧪 Test với ảnh khác</button>
                </div>

                <div class="status-message" id="statusMessage"></div>
            </div>
        </div>
    </div>

    <script src="region_selector.js"></script>
</body>
</html>
