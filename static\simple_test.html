<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto Grading Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Auto Grading Interface</h1>
        
        <div class="info">
            <strong>📋 Hướng dẫn:</strong><br>
            1. Chọn ảnh phiếu trả lời (JPG, PNG)<br>
            2. Chọn file Excel đáp án<br>
            3. Nhấn "Test API" để kiểm tra kết nối<br>
            4. Nhấn "Submit" để chấm điểm
        </div>

        <form id="testForm">
            <div class="form-group">
                <label for="imageFiles">📸 Chọn ảnh phiếu trả lời:</label>
                <input type="file" id="imageFiles" multiple accept="image/*" required>
            </div>

            <div class="form-group">
                <label for="excelFile">📊 Chọn file Excel đáp án:</label>
                <input type="file" id="excelFile" accept=".xlsx,.xls" required>
            </div>

            <button type="button" onclick="testAPI()" id="testBtn">🔍 Test API Connection</button>
            <br><br>
            <button type="submit" id="submitBtn">🚀 Submit for Grading</button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Đang xử lý...</p>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');
            
            testBtn.disabled = true;
            testBtn.textContent = '⏳ Testing...';
            
            try {
                // Test root endpoint
                const response = await fetch('http://localhost:8000/');
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ API Connection Successful!</strong><br>
                        Message: ${data.message}<br>
                        Docs: <a href="http://localhost:8000${data.docs}" target="_blank">${data.docs}</a><br>
                        Auto Grading: <a href="http://localhost:8000${data.auto_grading_interface || '/auto-grading'}" target="_blank">Interface</a>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ API Connection Failed!</strong><br>
                    Error: ${error.message}<br><br>
                    <strong>💡 Troubleshooting:</strong><br>
                    1. Make sure server is running: <code>uvicorn app.main:app --reload</code><br>
                    2. Check if port 8000 is available<br>
                    3. Try accessing: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a>
                `;
            }
            
            resultDiv.style.display = 'block';
            testBtn.disabled = false;
            testBtn.textContent = '🔍 Test API Connection';
        }

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const imageFiles = document.getElementById('imageFiles').files;
            const excelFile = document.getElementById('excelFile').files[0];
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            const submitBtn = document.getElementById('submitBtn');
            
            if (imageFiles.length === 0 || !excelFile) {
                alert('Vui lòng chọn đầy đủ file!');
                return;
            }
            
            // Show loading
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            submitBtn.disabled = true;
            
            const formData = new FormData();
            
            // Add image files
            Array.from(imageFiles).forEach(file => {
                formData.append('image_files', file);
            });
            
            // Add excel file
            formData.append('excel_file', excelFile);
            
            try {
                const response = await fetch(`${API_BASE_URL}/auto_grading/auto`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Grading Completed!</strong><br>
                        Message: ${data.message}<br>
                        Results: ${data.results ? data.results.length : 0} processed<br><br>
                        <details>
                            <summary>📋 Detailed Results</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ Grading Failed!</strong><br>
                        Status: ${response.status}<br>
                        Error: ${data.detail || JSON.stringify(data)}<br><br>
                        <details>
                            <summary>🔍 Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Request Failed!</strong><br>
                    Error: ${error.message}<br><br>
                    <strong>💡 Possible causes:</strong><br>
                    1. Server not running<br>
                    2. Network connection issue<br>
                    3. CORS policy blocking request
                `;
            }
            
            // Hide loading and show result
            loadingDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            submitBtn.disabled = false;
        });

        // Auto-test API on page load
        window.addEventListener('load', function() {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
