<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMR Debug Viewer - PlanBook AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
            text-align: center;
        }

        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }

        .upload-section.dragover {
            border-color: #4facfe;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .submit-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1em;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .processed-images {
            margin-bottom: 30px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .image-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .image-card-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .image-card-header h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .image-card-header p {
            color: #666;
            font-size: 0.9em;
            margin: 0;
        }

        .image-card-body {
            padding: 15px;
            text-align: center;
        }

        .processed-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .processed-image:hover {
            transform: scale(1.05);
        }

        .final-report {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .final-report h3 {
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .report-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .report-item label {
            font-weight: 600;
            display: block;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .report-item value {
            font-size: 1.2em;
            font-weight: bold;
        }

        .answers-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .answers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .answer-item {
            background: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 5px;
            text-align: center;
            font-size: 0.9em;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .file-info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #bee5eb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            max-height: 90%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #bbb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 OMR Debug Viewer</h1>
            <p>Upload ảnh phiếu trả lời để xem quá trình xử lý và kết quả trích xuất</p>
        </div>

        <div class="main-content">
            <div class="upload-section" id="uploadSection">
                <h3>📄 Chọn ảnh phiếu trả lời</h3>
                <p>Kéo thả file vào đây hoặc click để chọn</p>
                
                <div class="file-input-wrapper">
                    <input type="file" id="imageFile" class="file-input" accept="image/*" required>
                    <label for="imageFile" class="file-input-label">
                        📁 Chọn ảnh
                    </label>
                </div>
                
                <div id="fileInfo" class="file-info" style="display: none;"></div>
                
                <button type="button" class="submit-btn" id="submitBtn" disabled>
                    🚀 Xử lý OMR
                </button>
            </div>

            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <h3>Đang xử lý OMR...</h3>
                <p>Vui lòng đợi trong giây lát</p>
            </div>

            <div class="results-section" id="resultsSection">
                <div class="processed-images" id="processedImages">
                    <h2>📸 Ảnh đã qua xử lý</h2>
                    <div class="image-grid" id="imageGrid"></div>
                </div>

                <div class="final-report" id="finalReport">
                    <h3>📊 Báo cáo kết quả</h3>
                    <div class="report-grid" id="reportGrid"></div>
                    <div class="answers-section" id="answersSection"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for image viewing -->
    <div id="imageModal" class="modal">
        <span class="close">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        // API base URL
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        // Elements
        const uploadSection = document.getElementById('uploadSection');
        const imageFile = document.getElementById('imageFile');
        const submitBtn = document.getElementById('submitBtn');
        const loadingSection = document.getElementById('loadingSection');
        const resultsSection = document.getElementById('resultsSection');
        const fileInfo = document.getElementById('fileInfo');
        const imageGrid = document.getElementById('imageGrid');
        const reportGrid = document.getElementById('reportGrid');
        const answersSection = document.getElementById('answersSection');
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const closeModal = document.getElementsByClassName('close')[0];

        // Drag and drop functionality
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                imageFile.files = files;
                handleFileSelect();
            }
        });

        // File selection handler
        imageFile.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = imageFile.files[0];
            if (file) {
                fileInfo.style.display = 'block';
                fileInfo.innerHTML = `
                    <strong>📁 File đã chọn:</strong> ${file.name}<br>
                    <strong>📏 Kích thước:</strong> ${formatFileSize(file.size)}<br>
                    <strong>📅 Loại:</strong> ${file.type}
                `;
                submitBtn.disabled = false;
            } else {
                fileInfo.style.display = 'none';
                submitBtn.disabled = true;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Submit handler
        submitBtn.addEventListener('click', async function() {
            const file = imageFile.files[0];
            if (!file) {
                alert('Vui lòng chọn ảnh!');
                return;
            }

            showLoading();

            const formData = new FormData();
            formData.append('image_file', file);

            try {
                const response = await fetch(`${API_BASE_URL}/omr_debug/viewer`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showResults(result);
                } else {
                    showError(result.message || 'Lỗi xử lý OMR');
                }
            } catch (error) {
                showError('Lỗi kết nối: ' + error.message);
            } finally {
                hideLoading();
            }
        });

        function showLoading() {
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ Đang xử lý...';
            loadingSection.style.display = 'block';
            resultsSection.style.display = 'none';
        }

        function hideLoading() {
            submitBtn.disabled = false;
            submitBtn.textContent = '🚀 Xử lý OMR';
            loadingSection.style.display = 'none';
        }

        function showResults(data) {
            resultsSection.style.display = 'block';

            // Display processed images
            displayProcessedImages(data.processed_images || []);

            // Display final report
            displayFinalReport(data.final_report || {});
        }

        function displayProcessedImages(images) {
            imageGrid.innerHTML = '';

            images.forEach(imageData => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                
                imageCard.innerHTML = `
                    <div class="image-card-header">
                        <h4>Bước ${imageData.step}: ${imageData.name}</h4>
                        <p>${imageData.description}</p>
                    </div>
                    <div class="image-card-body">
                        <img src="${imageData.image}" alt="${imageData.name}" class="processed-image">
                    </div>
                `;

                // Add click handler for modal
                const img = imageCard.querySelector('.processed-image');
                img.addEventListener('click', function() {
                    modalImage.src = imageData.image;
                    modal.style.display = 'block';
                });

                imageGrid.appendChild(imageCard);
            });
        }

        function displayFinalReport(report) {
            // Basic info
            reportGrid.innerHTML = `
                <div class="report-item">
                    <label>Mã học sinh:</label>
                    <value>${report.student_code || 'N/A'}</value>
                </div>
                <div class="report-item">
                    <label>Mã đề thi:</label>
                    <value>${report.test_code || 'N/A'}</value>
                </div>
                <div class="report-item">
                    <label>Số câu trả lời:</label>
                    <value>${report.total_answers || 0}</value>
                </div>
                <div class="report-item">
                    <label>Trạng thái:</label>
                    <value>${report.processing_status === 'success' ? '✅ Thành công' : '❌ Thất bại'}</value>
                </div>
            `;

            // Answers
            if (report.extracted_answers && Object.keys(report.extracted_answers).length > 0) {
                let answersHTML = '<h4>📝 Câu trả lời đã trích xuất:</h4><div class="answers-grid">';
                
                Object.entries(report.extracted_answers).forEach(([questionNum, answer]) => {
                    answersHTML += `
                        <div class="answer-item">
                            <strong>Câu ${questionNum}:</strong><br>
                            ${answer || 'Trống'}
                        </div>
                    `;
                });
                
                answersHTML += '</div>';
                answersSection.innerHTML = answersHTML;
            } else {
                answersSection.innerHTML = '<h4>⚠️ Không trích xuất được câu trả lời</h4>';
            }

            if (report.error) {
                answersSection.innerHTML += `<div class="error-message"><strong>Lỗi:</strong> ${report.error}</div>`;
            }
        }

        function showError(message) {
            resultsSection.style.display = 'block';
            imageGrid.innerHTML = '';
            reportGrid.innerHTML = '';
            answersSection.innerHTML = `
                <div class="error-message">
                    <strong>❌ Có lỗi xảy ra:</strong><br>
                    ${message}
                </div>
            `;
        }

        // Modal handlers
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    </script>
</body>
</html>
