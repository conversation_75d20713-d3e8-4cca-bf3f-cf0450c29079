#!/usr/bin/env python3
"""
Script test API auto grading
"""

import requests
import os
from pathlib import Path

def test_auto_grading_api():
    """Test API auto grading với file mẫu"""
    
    # URL API
    api_url = "http://localhost:8000/api/v1/auto_grading/auto"
    
    # Đường dẫn file Excel mẫu
    excel_file_path = "static/demo_data/detailed_answer_key.xlsx"
    
    # Kiểm tra file Excel có tồn tại không
    if not os.path.exists(excel_file_path):
        print(f"❌ File Excel không tồn tại: {excel_file_path}")
        print("💡 Chạy lệnh: python static/create_sample_excel.py để tạo file mẫu")
        return False
    
    print("🚀 Testing Auto Grading API...")
    print(f"📊 Sử dụng file Excel: {excel_file_path}")
    
    # Chuẩn bị files để upload
    files = {
        'excel_file': ('answer_key.xlsx', open(excel_file_path, 'rb'), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    }
    
    # Tìm ảnh test trong thư mục data (nếu có)
    image_paths = []
    data_dir = Path("data")
    if data_dir.exists():
        # Tìm ảnh trong các thư mục con
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            image_paths.extend(data_dir.rglob(ext))
    
    if not image_paths:
        print("⚠️ Không tìm thấy ảnh test trong thư mục data/")
        print("💡 Vui lòng thêm ảnh phiếu trả lời vào thư mục data/ để test")
        print("🔧 API test sẽ chỉ kiểm tra endpoint mà không xử lý ảnh")
        
        # Test endpoint health
        try:
            # Chỉ gửi file Excel để test endpoint
            response = requests.post(api_url, files=files)
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code == 422:
                print("✅ API endpoint hoạt động (cần thêm image_files)")
                print("📋 Response:", response.json())
                return True
            else:
                print("❌ API response không như mong đợi")
                print("📋 Response:", response.text)
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Không thể kết nối đến API server")
            print("💡 Hãy đảm bảo server đang chạy tại http://localhost:8000")
            return False
        except Exception as e:
            print(f"❌ Lỗi khi test API: {str(e)}")
            return False
        finally:
            files['excel_file'][1].close()
    
    else:
        print(f"📸 Tìm thấy {len(image_paths)} ảnh test")
        
        # Thêm ảnh vào request
        for i, img_path in enumerate(image_paths[:3]):  # Chỉ test với 3 ảnh đầu
            files[f'image_files'] = (img_path.name, open(img_path, 'rb'), 'image/jpeg')
        
        try:
            print("📤 Gửi request đến API...")
            response = requests.post(api_url, files=files)
            
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ API test thành công!")
                print(f"📊 Kết quả: {result.get('message', 'No message')}")
                
                if 'results' in result:
                    print(f"📋 Số bài đã chấm: {len(result['results'])}")
                    for i, res in enumerate(result['results'][:2]):  # Hiển thị 2 kết quả đầu
                        print(f"   Bài {i+1}: {res.get('filename', 'Unknown')} - {res.get('status', 'Unknown')}")
                        if res.get('status') == 'success':
                            print(f"      Điểm: {res.get('score', 'N/A')}/10")
                
                return True
            else:
                print("❌ API test thất bại")
                print("📋 Response:", response.text)
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Không thể kết nối đến API server")
            print("💡 Hãy đảm bảo server đang chạy tại http://localhost:8000")
            return False
        except Exception as e:
            print(f"❌ Lỗi khi test API: {str(e)}")
            return False
        finally:
            # Đóng tất cả file handles
            for key, file_tuple in files.items():
                if hasattr(file_tuple[1], 'close'):
                    file_tuple[1].close()

def test_server_health():
    """Kiểm tra server có đang chạy không"""
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Server đang chạy")
            return True
        else:
            print(f"⚠️ Server response: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server không chạy hoặc không thể kết nối")
        return False

def main():
    print("🔍 Auto Grading API Test")
    print("=" * 50)
    
    # Kiểm tra server
    if not test_server_health():
        print("\n💡 Để khởi động server:")
        print("   python -m uvicorn app.main:app --reload")
        print("   hoặc chạy: start_auto_grading.bat")
        return
    
    print()
    
    # Test API
    success = test_auto_grading_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test hoàn thành thành công!")
        print("🌐 Truy cập giao diện web: http://localhost:8000/auto-grading")
    else:
        print("❌ Test thất bại. Vui lòng kiểm tra lại cấu hình.")

if __name__ == "__main__":
    main()
